[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m43[31m:22:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m43[31m:22:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m44[31m:37:[0m Missing newline after "(" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m44[31m:37:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m44[31m:63:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m45[31m:109:[0m Missing newline before ")" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m45[31m:110:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m45[31m:110:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m57[31m:44:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m61[31m:75:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m74[31m:38:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m76[31m:44:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m77[31m:59:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m77[31m:74:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m77[31m:92:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/datasources/remote/[0mProxyRemoteDataSource.kt[31m:[0m91[31m:39:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m40[31m:21:[0m Property name should start with a lowercase letter and use camel case [31m(standard:property-naming)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m44[31m:21:[0m Property name should start with a lowercase letter and use camel case [31m(standard:property-naming)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m79[31m:45:[0m Missing newline after "(" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m79[31m:45:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m79[31m:74:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:31:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:72:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:107:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:139:[0m Missing newline before ")" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:139:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:140:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m80[31m:140:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m83[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m83[31m:45:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m83[31m:74:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m83[31m:100:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m83[31m:124:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m83[31m:136:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m92[31m:34:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m103[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m103[31m:45:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m103[31m:74:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m103[31m:98:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m103[31m:121:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m103[31m:133:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m115[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m115[31m:55:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m115[31m:84:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m115[31m:94:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m115[31m:110:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m115[31m:141:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/data/repositories/[0mProxyRepositoryImpl.kt[31m:[0m138[31m:34:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m28[31m:37:[0m Missing newline after "(" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m28[31m:37:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m28[31m:64:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m29[31m:97:[0m Missing newline before ")" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m29[31m:98:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m29[31m:98:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/domain/usecases/[0mStartProxyUseCase.kt[31m:[0m38[31m:22:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/managers/[0mProxyManager.kt[31m:[0m173[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/managers/[0mProxyManager.kt[31m:[0m173[31m:36:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/managers/[0mProxyManager.kt[31m:[0m173[31m:52:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/managers/[0mProxyManager.kt[31m:[0m173[31m:125:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/managers/[0mProxyManager.kt[31m:[0m176[31m:36:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/managers/[0mProxyManager.kt[31m:[0m180[31m:47:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m21[31m:1:[0m Wildcard import [31m(standard:no-wildcard-imports)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m107[31m:28:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m111[31m:43:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m121[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m121[31m:36:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m121[31m:55:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m121[31m:124:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m308[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m320[31m:39:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m418[31m:38:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m418[31m:64:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m418[31m:89:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/services/[0mProxyVpnService.kt[31m:[0m418[31m:104:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m1[31m:9:[0m Package name must not contain underscore [31m(standard:package-name)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m11[31m:1:[0m Class body should not start with blank line [31m(standard:no-empty-first-line-in-class-body)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m11[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m13[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m14[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m36[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m37[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m40[31m:11:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m40[31m:24:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m40[31m:41:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m40[31m:69:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m50[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m51[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m54[31m:11:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m54[31m:24:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m54[31m:41:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m54[31m:69:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m64[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m65[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m68[31m:11:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m68[31m:24:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m68[31m:41:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m68[31m:69:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m78[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m79[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m82[31m:11:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m82[31m:24:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m82[31m:41:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m82[31m:69:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m92[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m93[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m96[31m:11:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m96[31m:24:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m96[31m:41:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m96[31m:69:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m106[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m107[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m110[31m:21:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m110[31m:34:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m110[31m:54:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m110[31m:92:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m112[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m120[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m121[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m124[31m:20:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m124[31m:33:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m124[31m:53:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m124[31m:72:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m130[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m131[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m134[31m:32:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m134[31m:45:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m134[31m:65:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m134[31m:79:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m145[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m146[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m149[31m:21:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m149[31m:34:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m149[31m:46:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m149[31m:56:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m149[31m:76:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m155[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m156[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m159[31m:24:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m159[31m:37:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m159[31m:53:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m159[31m:66:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m159[31m:107:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m169[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m170[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m173[31m:25:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m173[31m:38:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m173[31m:49:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m173[31m:66:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m173[31m:84:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m179[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m180[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m183[31m:27:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m183[31m:40:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m183[31m:59:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m183[31m:74:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m183[31m:94:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m189[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m190[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m193[31m:20:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m193[31m:33:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m193[31m:49:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m193[31m:88:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m195[31m:30:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m203[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m204[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m207[31m:22:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m207[31m:35:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m207[31m:48:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m207[31m:64:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m207[31m:78:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m212[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m213[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m216[31m:21:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m216[31m:34:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m216[31m:50:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m216[31m:65:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m216[31m:82:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m222[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m223[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m226[31m:18:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m226[31m:31:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m226[31m:46:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDebugLogger.kt[31m:[0m226[31m:66:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m1[31m:9:[0m Package name must not contain underscore [31m(standard:package-name)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m12[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m19[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m20[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m26[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m30[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m33[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m35[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m40[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m43[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m54[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m55[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m61[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m66[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m78[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m79[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m85[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m88[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m91[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m93[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m96[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m107[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m108[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m114[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m117[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m130[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m131[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m138[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m142[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m144[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m148[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m160[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m161[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m167[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m171[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m174[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m178[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m181[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m184[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m188[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m192[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m196[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m200[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m212[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m213[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m216[31m:33:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m216[31m:53:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m216[31m:74:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m218[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m222[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m230[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m233[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m234[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m237[31m:35:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m237[31m:55:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m237[31m:75:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m239[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m242[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m244[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m247[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m250[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m253[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m254[31m:5:[0m Expected a blank line for this declaration [31m(standard:blank-line-before-declaration)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m262[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m265[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m269[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m275[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m279[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m283[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m287[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m291[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mDomainExtractor.kt[31m:[0m296[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m1[31m:9:[0m Package name must not contain underscore [31m(standard:package-name)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m24[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m33[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m49[31m:25:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m49[31m:45:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m49[31m:60:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m49[31m:73:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m49[31m:73:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m51[31m:37:[0m Missing newline after "(" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m51[31m:37:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m51[31m:62:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:23:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:49:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:75:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:103:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:129:[0m Missing newline before ")" [31m(standard:wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:129:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:130:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m52[31m:130:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m86[31m:39:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m86[31m:63:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m86[31m:87:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m89[31m:31:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m94[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m97[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m101[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m130[31m:45:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m130[31m:69:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m130[31m:93:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m136[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m144[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m147[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m151[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m172[31m:24:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-declaration-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m179[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m185[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m195[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m212[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m224[31m:40:[0m Newline expected after opening parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m224[31m:60:[0m Parameter should start on a newline [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m224[31m:75:[0m Newline expected before closing parenthesis [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m225[31m:27:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/infrastructure/utils/[0mSocks5Client.kt[31m:[0m231[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mProxyViewModel.kt[31m:[0m13[31m:1:[0m Wildcard import [31m(standard:no-wildcard-imports)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mProxyViewModel.kt[31m:[0m174[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mProxyViewModel.kt[31m:[0m174[31m:31:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mProxyViewModel.kt[31m:[0m174[31m:59:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mProxyViewModel.kt[31m:[0m174[31m:133:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m151[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m151[31m:47:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m151[31m:75:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m151[31m:124:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m153[31m:50:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m160[31m:50:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m164[31m:30:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m169[31m:38:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:41:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:69:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:92:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:98:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:120:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m182[31m:121:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m188[31m:39:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m193[31m:42:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m218[31m:1:[0m Exceeded max line length (120) [31m(standard:max-line-length)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m218[31m:51:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m218[31m:79:[0m Argument should be on a separate line (unless all arguments can fit a single line) [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m218[31m:141:[0m Missing newline before ")" [31m(standard:argument-list-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m219[31m:54:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m224[31m:54:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m232[31m:50:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m236[31m:30:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m241[31m:38:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m287[31m:38:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/main/java/com/android/proxy_self/presentation/viewmodel/[0mSettingsViewModel.kt[31m:[0m293[31m:38:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m

Summary error count (descending) by rule:
  standard:function-signature: 97
  standard:no-trailing-spaces: 88
  standard:argument-list-wrapping: 57
  standard:blank-line-before-declaration: 26
  standard:multiline-expression-wrapping: 26
  standard:max-line-length: 12
  standard:trailing-comma-on-call-site: 10
  standard:wrapping: 8
  standard:package-name: 3
  standard:no-wildcard-imports: 2
  standard:property-naming: 2
  standard:no-empty-first-line-in-class-body: 1
  standard:trailing-comma-on-declaration-site: 1
