[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m1[31m:9:[0m Package name must not contain underscore [31m(standard:package-name)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m22[31m:1:[0m Class body should not start with blank line [31m(standard:no-empty-first-line-in-class-body)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m26[31m:30:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m37[31m:47:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m43[31m:20:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m45[31m:38:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m56[31m:41:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m56[31m:41:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m74[31m:36:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m74[31m:36:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m91[31m:35:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m91[31m:35:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m96[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m99[31m:51:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m111[31m:35:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m111[31m:35:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m124[31m:28:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m124[31m:28:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m137[31m:35:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m137[31m:35:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m154[31m:30:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m154[31m:30:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m169[31m:31:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m169[31m:31:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m184[31m:35:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m184[31m:35:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m201[31m:34:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m201[31m:34:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m218[31m:31:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m218[31m:31:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m236[31m:32:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m236[31m:32:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m251[31m:37:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m251[31m:37:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m258[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m273[31m:31:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/data/local/[0mProxyDatabaseTest.kt[31m:[0m273[31m:31:[0m Newline expected before expression body [31m(standard:function-signature)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m1[31m:9:[0m Package name must not contain underscore [31m(standard:package-name)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m21[31m:1:[0m Class body should not start with blank line [31m(standard:no-empty-first-line-in-class-body)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m27[31m:30:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m36[31m:25:[0m Missing trailing comma before ")" [31m(standard:trailing-comma-on-call-site)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m48[31m:30:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m60[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m75[31m:27:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m82[31m:26:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m94[31m:26:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m109[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m124[31m:35:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m141[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m157[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m173[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m188[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m196[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m198[31m:1:[0m Trailing space(s) [31m(standard:no-trailing-spaces)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m209[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m
[31m/home/<USER>/work/proxy_self/proxy_self/app/src/androidTest/java/com/android/proxy_self/infrastructure/services/[0mProxyServiceTest.kt[31m:[0m226[31m:29:[0m A multiline expression should start on a new line [31m(standard:multiline-expression-wrapping)[0m

Summary error count (descending) by rule:
  standard:multiline-expression-wrapping: 31
  standard:function-signature: 14
  standard:trailing-comma-on-call-site: 4
  standard:no-trailing-spaces: 3
  standard:no-empty-first-line-in-class-body: 2
  standard:package-name: 2
