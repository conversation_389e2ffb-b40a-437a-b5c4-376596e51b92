$dokka.format:html-v1
$dokka.linkExtension:html
$dokka.location:com.android.proxy_self.data.datasources.local////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/index.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/index.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/deleteAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/delete-all-configs.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/deleteConfig/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/delete-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/deleteConfigById/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/delete-config-by-id.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/disableAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/disable-all-configs.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/enableConfig/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/enable-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/enableConfigById/#kotlin.Long#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/enable-config-by-id.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/getAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-all-configs.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/getConfigById/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-config-by-id.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/getConfigCount/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-config-count.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/getEnabledConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-enabled-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/getLatestConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/get-latest-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/insertConfig/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/insert-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDao/updateConfig/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-dao/update-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDatabase.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/-companion/index.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDatabase.Companion/getDatabase/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/-companion/get-database.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDatabase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/index.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDatabase/ProxyDatabase/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/-proxy-database.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyDatabase/proxyDao/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-database/proxy-dao.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/index.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/ProxyEntity/#kotlin.Long#kotlin.String#kotlin.Int#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.Boolean#kotlin.Long#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/-proxy-entity.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/createdAt/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/created-at.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/domains/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/domains.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/encryptedPassword/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/encrypted-password.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/id/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/id.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/isEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/is-enabled.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/proxyType/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/proxy-type.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/serverAddress/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/server-address.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/serverPort/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/server-port.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/updatedAt/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/updated-at.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyEntity/username/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-entity/username.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/index.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/ProxyLocalDataSource/#com.android.proxy_self.data.datasources.local.ProxyDao/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/-proxy-local-data-source.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/deleteAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/delete-all-configs.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/deleteConfig/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/delete-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/deleteConfigById/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/delete-config-by-id.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/disableAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/disable-all-configs.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/enableConfig/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/enable-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/getAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-all-configs.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/getConfigById/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-config-by-id.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/getConfigCount/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-config-count.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/getEnabledConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-enabled-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/getLatestConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/get-latest-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/saveConfig/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/save-config.html
$dokka.location:com.android.proxy_self.data.datasources.local/ProxyLocalDataSource/updateConfig/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.local/-proxy-local-data-source/update-config.html
$dokka.location:com.android.proxy_self.data.datasources.remote////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/index.html
$dokka.location:com.android.proxy_self.data.datasources.remote/ProxyRemoteDataSource///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/index.html
$dokka.location:com.android.proxy_self.data.datasources.remote/ProxyRemoteDataSource/ProxyRemoteDataSource/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/-proxy-remote-data-source.html
$dokka.location:com.android.proxy_self.data.datasources.remote/ProxyRemoteDataSource/getConfigFromCloud/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/get-config-from-cloud.html
$dokka.location:com.android.proxy_self.data.datasources.remote/ProxyRemoteDataSource/syncConfigToCloud/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/sync-config-to-cloud.html
$dokka.location:com.android.proxy_self.data.datasources.remote/ProxyRemoteDataSource/testProxyConnection/#kotlin.String#kotlin.Int#kotlin.String#kotlin.String#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/-proxy-remote-data-source/test-proxy-connection.html
$dokka.location:com.android.proxy_self.data.mappers////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/index.html
$dokka.location:com.android.proxy_self.data.mappers/ProxyMapper///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/index.html
$dokka.location:com.android.proxy_self.data.mappers/ProxyMapper/ProxyMapper/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/-proxy-mapper.html
$dokka.location:com.android.proxy_self.data.mappers/ProxyMapper/mapToDomain/#com.android.proxy_self.data.datasources.local.ProxyEntity#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/map-to-domain.html
$dokka.location:com.android.proxy_self.data.mappers/ProxyMapper/mapToDomainList/#kotlin.collections.List[com.android.proxy_self.data.datasources.local.ProxyEntity]#kotlin.Function1[kotlin.String,kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/map-to-domain-list.html
$dokka.location:com.android.proxy_self.data.mappers/ProxyMapper/mapToEntity/#com.android.proxy_self.domain.entities.ProxyConfig#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/map-to-entity.html
$dokka.location:com.android.proxy_self.data.mappers/ProxyMapper/updateTimestamp/#com.android.proxy_self.data.datasources.local.ProxyEntity/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.mappers/-proxy-mapper/update-timestamp.html
$dokka.location:com.android.proxy_self.data.repositories////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/index.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/index.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/ProxyRepositoryImpl/#android.content.Context#com.android.proxy_self.data.datasources.local.ProxyLocalDataSource#com.android.proxy_self.data.datasources.remote.ProxyRemoteDataSource#com.android.proxy_self.data.mappers.ProxyMapper#com.android.proxy_self.infrastructure.utils.EncryptionUtils/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/-proxy-repository-impl.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/deleteConfig/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/delete-config.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/getAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/get-all-configs.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/getProxyConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/get-proxy-config.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/getProxyStatus/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/get-proxy-status.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/isProxyEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/is-proxy-enabled.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/saveProxyConfig/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/save-proxy-config.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/startProxy/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/start-proxy.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/stopProxy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/stop-proxy.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/testConnection/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/test-connection.html
$dokka.location:com.android.proxy_self.data.repositories/ProxyRepositoryImpl/updateConfig/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.data.repositories/-proxy-repository-impl/update-config.html
$dokka.location:com.android.proxy_self.di////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/index.html
$dokka.location:com.android.proxy_self.di/DatabaseModule///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-database-module/index.html
$dokka.location:com.android.proxy_self.di/DatabaseModule/provideProxyDao/#com.android.proxy_self.data.datasources.local.ProxyDatabase/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-database-module/provide-proxy-dao.html
$dokka.location:com.android.proxy_self.di/DatabaseModule/provideProxyDatabase/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-database-module/provide-proxy-database.html
$dokka.location:com.android.proxy_self.di/ManagerModule///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-manager-module/index.html
$dokka.location:com.android.proxy_self.di/ManagerModule/provideNetworkManager/#android.content.Context#com.android.proxy_self.infrastructure.utils.NetworkUtils/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-manager-module/provide-network-manager.html
$dokka.location:com.android.proxy_self.di/ManagerModule/provideProxyManager/#com.android.proxy_self.infrastructure.utils.NetworkUtils/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-manager-module/provide-proxy-manager.html
$dokka.location:com.android.proxy_self.di/ManagerModule/provideProxyNotificationManager/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-manager-module/provide-proxy-notification-manager.html
$dokka.location:com.android.proxy_self.di/NetworkModule///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/index.html
$dokka.location:com.android.proxy_self.di/NetworkModule/provideEncryptionUtils/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-encryption-utils.html
$dokka.location:com.android.proxy_self.di/NetworkModule/provideNetworkUtils/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-network-utils.html
$dokka.location:com.android.proxy_self.di/NetworkModule/provideOkHttpClient/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-ok-http-client.html
$dokka.location:com.android.proxy_self.di/NetworkModule/provideRetrofit/#okhttp3.OkHttpClient/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-retrofit.html
$dokka.location:com.android.proxy_self.di/NetworkModule/provideSharedPreferences/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-shared-preferences.html
$dokka.location:com.android.proxy_self.di/NetworkModule/provideValidationUtils/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-network-module/provide-validation-utils.html
$dokka.location:com.android.proxy_self.di/RepositoryModule///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-repository-module/index.html
$dokka.location:com.android.proxy_self.di/RepositoryModule/RepositoryModule/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-repository-module/-repository-module.html
$dokka.location:com.android.proxy_self.di/RepositoryModule/bindProxyRepository/#com.android.proxy_self.data.repositories.ProxyRepositoryImpl/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.di/-repository-module/bind-proxy-repository.html
$dokka.location:com.android.proxy_self.domain.entities////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/-companion/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig.Companion/fromDomainsString/#kotlin.Long#kotlin.String#kotlin.Int#kotlin.String#kotlin.String#com.android.proxy_self.domain.entities.ProxyType#kotlin.String#kotlin.Boolean#kotlin.Long#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/-companion/from-domains-string.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/ProxyConfig/#kotlin.Long#kotlin.String#kotlin.Int#kotlin.String#kotlin.String#com.android.proxy_self.domain.entities.ProxyType#kotlin.collections.List[kotlin.String]#kotlin.Boolean#kotlin.Long#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/-proxy-config.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/createdAt/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/created-at.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/domains/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/domains.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/getDomainsAsString/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/get-domains-as-string.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/id/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/id.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/isEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/is-enabled.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/isValid/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/is-valid.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/password/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/password.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/proxyType/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/proxy-type.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/serverAddress/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/server-address.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/serverPort/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/server-port.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/updatedAt/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/updated-at.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyConfig/username/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-config/username.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus.CONNECTED///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-c-o-n-n-e-c-t-e-d/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus.CONNECTING///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-c-o-n-n-e-c-t-i-n-g/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus.DISCONNECTED///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-d-i-s-c-o-n-n-e-c-t-e-d/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus.ERROR///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-e-r-r-o-r/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus.RECONNECTING///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/-r-e-c-o-n-n-e-c-t-i-n-g/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus/displayName/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/display-name.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus/entries/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/entries.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus/hasError/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/has-error.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus/isActive/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/is-active.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus/valueOf/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/value-of.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatus/values/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status/values.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/ProxyStatusInfo/#com.android.proxy_self.domain.entities.ProxyStatus#kotlin.Long?#kotlin.String?#kotlin.Long#kotlin.Int#kotlin.collections.List[kotlin.String]#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/-proxy-status-info.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/activeConnections/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/active-connections.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/activeDomains/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/active-domains.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/bytesTransferred/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/bytes-transferred.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/connectionTime/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/connection-time.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/getConnectionDuration/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/get-connection-duration.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/getFormattedBytesTransferred/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/get-formatted-bytes-transferred.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/lastError/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/last-error.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/status/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/status.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyStatusInfo/timestamp/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-status-info/timestamp.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-companion/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType.Companion/fromValue/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-companion/from-value.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType.Companion/getDisplayNames/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-companion/get-display-names.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType.HTTP///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-h-t-t-p/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType.SOCKS5///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/-s-o-c-k-s5/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/index.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType/displayName/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/display-name.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType/entries/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/entries.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType/value/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/value.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType/valueOf/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/value-of.html
$dokka.location:com.android.proxy_self.domain.entities/ProxyType/values/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.entities/-proxy-type/values.html
$dokka.location:com.android.proxy_self.domain.repositories////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/index.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/index.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/deleteConfig/#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/delete-config.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/getAllConfigs/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/get-all-configs.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/getProxyConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/get-proxy-config.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/getProxyStatus/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/get-proxy-status.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/isProxyEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/is-proxy-enabled.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/saveProxyConfig/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/save-proxy-config.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/startProxy/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/start-proxy.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/stopProxy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/stop-proxy.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/testConnection/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/test-connection.html
$dokka.location:com.android.proxy_self.domain.repositories/ProxyRepository/updateConfig/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.repositories/-proxy-repository/update-config.html
$dokka.location:com.android.proxy_self.domain.usecases.base////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/index.html
$dokka.location:com.android.proxy_self.domain.usecases.base//asFailure/kotlin.Throwable#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/as-failure.html
$dokka.location:com.android.proxy_self.domain.usecases.base//asSuccess/TypeParam(bounds=[kotlin.Any?])#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/as-success.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/index.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCase/BaseUseCase/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/-base-use-case.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCase/execute/#TypeParam(bounds=[kotlin.Any?])/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/execute.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCase/invoke/#TypeParam(bounds=[kotlin.Any?])/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case/invoke.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCaseNoParams///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/index.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCaseNoParams/BaseUseCaseNoParams/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/-base-use-case-no-params.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCaseNoParams/execute/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/execute.html
$dokka.location:com.android.proxy_self.domain.usecases.base/BaseUseCaseNoParams/invoke/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/-base-use-case-no-params/invoke.html
$dokka.location:com.android.proxy_self.domain.usecases////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/index.html
$dokka.location:com.android.proxy_self.domain.usecases/GetProxyConfigUseCase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/index.html
$dokka.location:com.android.proxy_self.domain.usecases/GetProxyConfigUseCase/GetProxyConfigUseCase/#com.android.proxy_self.domain.repositories.ProxyRepository/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/-get-proxy-config-use-case.html
$dokka.location:com.android.proxy_self.domain.usecases/GetProxyConfigUseCase/execute/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/execute.html
$dokka.location:com.android.proxy_self.domain.usecases/SaveProxyConfigUseCase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/index.html
$dokka.location:com.android.proxy_self.domain.usecases/SaveProxyConfigUseCase/SaveProxyConfigUseCase/#com.android.proxy_self.domain.repositories.ProxyRepository/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/-save-proxy-config-use-case.html
$dokka.location:com.android.proxy_self.domain.usecases/SaveProxyConfigUseCase/execute/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/execute.html
$dokka.location:com.android.proxy_self.domain.usecases/StartProxyUseCase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-start-proxy-use-case/index.html
$dokka.location:com.android.proxy_self.domain.usecases/StartProxyUseCase/StartProxyUseCase/#com.android.proxy_self.domain.repositories.ProxyRepository/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-start-proxy-use-case/-start-proxy-use-case.html
$dokka.location:com.android.proxy_self.domain.usecases/StartProxyUseCase/execute/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-start-proxy-use-case/execute.html
$dokka.location:com.android.proxy_self.domain.usecases/StopProxyUseCase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-stop-proxy-use-case/index.html
$dokka.location:com.android.proxy_self.domain.usecases/StopProxyUseCase/StopProxyUseCase/#com.android.proxy_self.domain.repositories.ProxyRepository/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-stop-proxy-use-case/-stop-proxy-use-case.html
$dokka.location:com.android.proxy_self.domain.usecases/StopProxyUseCase/execute/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-stop-proxy-use-case/execute.html
$dokka.location:com.android.proxy_self.domain.usecases/TestProxyConnectionUseCase///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/index.html
$dokka.location:com.android.proxy_self.domain.usecases/TestProxyConnectionUseCase/TestProxyConnectionUseCase/#com.android.proxy_self.domain.repositories.ProxyRepository/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/-test-proxy-connection-use-case.html
$dokka.location:com.android.proxy_self.domain.usecases/TestProxyConnectionUseCase/execute/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/execute.html
$dokka.location:com.android.proxy_self.infrastructure.managers////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/NetworkInfo/#kotlin.Boolean#kotlin.String#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/-network-info.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/connectionType/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/connection-type.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/isCellular/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-cellular.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/isConnected/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-connected.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/isMetered/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-metered.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/isWifi/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/is-wifi.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkInfo/timestamp/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-info/timestamp.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/NetworkManager/#android.content.Context#com.android.proxy_self.infrastructure.utils.NetworkUtils/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/-network-manager.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/extractDomain/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/extract-domain.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/getActiveNetworkCapabilities/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-active-network-capabilities.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/getCurrentNetworkState/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-current-network-state.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/getNetworkInfo/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-network-info.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/getNetworkStats/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/get-network-stats.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/isHostReachable/#kotlin.String#kotlin.Int#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/is-host-reachable.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/isVpnActive/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/is-vpn-active.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/observeNetworkConnectivity/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/observe-network-connectivity.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkManager/shouldRouteThoughProxy/#kotlin.String#kotlin.collections.List[kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-manager/should-route-though-proxy.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState.Available///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/-available/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState.Limited///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/-limited/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState.Lost///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/-lost/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState/entries/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/entries.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState/valueOf/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/value-of.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkState/values/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-state/values.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats/NetworkStats/#kotlin.Long#kotlin.Long#kotlin.Long#kotlin.Long#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/-network-stats.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats/bytesReceived/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/bytes-received.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats/bytesSent/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/bytes-sent.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats/packetsReceived/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/packets-received.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats/packetsSent/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/packets-sent.html
$dokka.location:com.android.proxy_self.infrastructure.managers/NetworkStats/timestamp/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-network-stats/timestamp.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/ProxyConnectionInfo/#kotlin.String#kotlin.Int#kotlin.String#kotlin.String#kotlin.Int#kotlin.collections.List[kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/-proxy-connection-info.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/domainCount/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/domain-count.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/domains/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/domains.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/proxyType/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/proxy-type.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/serverAddress/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/server-address.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/serverPort/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/server-port.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyConnectionInfo/username/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-connection-info/username.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/ProxyManager/#com.android.proxy_self.infrastructure.utils.NetworkUtils/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/-proxy-manager.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/createSocksProxy/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/create-socks-proxy.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/getProxyConnectionInfo/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/get-proxy-connection-info.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/shouldUseProxy/#kotlin.String#kotlin.collections.List[kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/should-use-proxy.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/stopSocksProxy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/stop-socks-proxy.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/testProxyConnection/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/test-proxy-connection.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyManager/validateProxyConfig/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-manager/validate-proxy-config.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager.Companion/CHANNEL_ID/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-c-h-a-n-n-e-l_-i-d.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager.Companion/NOTIFICATION_ID/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-n-o-t-i-f-i-c-a-t-i-o-n_-i-d.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager.Companion/OPEN_APP_ACTION/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-o-p-e-n_-a-p-p_-a-c-t-i-o-n.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager.Companion/STOP_ACTION/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-companion/-s-t-o-p_-a-c-t-i-o-n.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager/ProxyNotificationManager/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/-proxy-notification-manager.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager/cancelAllNotifications/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/cancel-all-notifications.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager/cancelNotification/#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/cancel-notification.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager/createServiceNotification/#com.android.proxy_self.domain.entities.ProxyStatus#kotlin.String#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/create-service-notification.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager/showStatusNotification/#kotlin.String#kotlin.String#kotlin.Boolean/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/show-status-notification.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyNotificationManager/updateNotification/#com.android.proxy_self.domain.entities.ProxyStatus#kotlin.String#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-notification-manager/update-notification.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyValidationResult.Invalid///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-invalid/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyValidationResult.Invalid/Invalid/#kotlin.collections.List[kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-invalid/-invalid.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyValidationResult.Invalid/errors/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-invalid/errors.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyValidationResult.Valid///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/-valid/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/ProxyValidationResult///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-proxy-validation-result/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult.Error///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-error/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult.Error/Error/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-error/-error.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult.Error/message/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-error/message.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult.Success///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-success/index.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult.Success/Success/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-success/-success.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult.Success/message/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/-success/message.html
$dokka.location:com.android.proxy_self.infrastructure.managers/SocksProxyResult///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/-socks-proxy-result/index.html
$dokka.location:com.android.proxy_self.infrastructure.receivers////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/index.html
$dokka.location:com.android.proxy_self.infrastructure.receivers/BootReceiver.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/-companion/index.html
$dokka.location:com.android.proxy_self.infrastructure.receivers/BootReceiver///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/index.html
$dokka.location:com.android.proxy_self.infrastructure.receivers/BootReceiver/BootReceiver/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/-boot-receiver.html
$dokka.location:com.android.proxy_self.infrastructure.receivers/BootReceiver/cancelAutoStartWork/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/cancel-auto-start-work.html
$dokka.location:com.android.proxy_self.infrastructure.receivers/BootReceiver/onReceive/#android.content.Context#android.content.Intent/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/on-receive.html
$dokka.location:com.android.proxy_self.infrastructure.receivers/BootReceiver/sharedPreferences/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/-boot-receiver/shared-preferences.html
$dokka.location:com.android.proxy_self.infrastructure.services////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/IpPacket/#kotlin.Int#kotlin.Int#kotlin.ByteArray#kotlin.ByteArray#kotlin.ByteArray/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/-ip-packet.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/data/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/data.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/destIp/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/dest-ip.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/equals/#kotlin.Any?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/equals.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/hashCode/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/hash-code.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/protocol/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/protocol.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/sourceIp/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/source-ip.html
$dokka.location:com.android.proxy_self.infrastructure.services/IpPacket/version/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-ip-packet/version.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.Companion/ACTION_START_PROXY/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-a-c-t-i-o-n_-s-t-a-r-t_-p-r-o-x-y.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.Companion/ACTION_STOP_PROXY/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-a-c-t-i-o-n_-s-t-o-p_-p-r-o-x-y.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.Companion/ACTION_UPDATE_CONFIG/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-a-c-t-i-o-n_-u-p-d-a-t-e_-c-o-n-f-i-g.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.Companion/EXTRA_PROXY_CONFIG/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-companion/-e-x-t-r-a_-p-r-o-x-y_-c-o-n-f-i-g.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.ProxyServiceBinder///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service-binder/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.ProxyServiceBinder/ProxyServiceBinder/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service-binder/-proxy-service-binder.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService.ProxyServiceBinder/getService/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service-binder/get-service.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/ProxyService/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/-proxy-service.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/getCurrentStatus/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/get-current-status.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/isEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/is-enabled.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/isProxyEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/is-proxy-enabled.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/notificationManager/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/notification-manager.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/onBind/#android.content.Intent?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-bind.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/onCreate/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-create.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/onDestroy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-destroy.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/onStartCommand/#android.content.Intent?#kotlin.Int#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/on-start-command.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/proxyManager/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/proxy-manager.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/proxyStatus/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/proxy-status.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/startProxy/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/start-proxy.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/stopProxy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/stop-proxy.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyService/updateConfig/#com.android.proxy_self.domain.entities.ProxyConfig/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-service/update-config.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService.Companion/ACTION_START_VPN/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/-a-c-t-i-o-n_-s-t-a-r-t_-v-p-n.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService.Companion/ACTION_STOP_VPN/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/-a-c-t-i-o-n_-s-t-o-p_-v-p-n.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService.Companion/EXTRA_PROXY_CONFIG/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-companion/-e-x-t-r-a_-p-r-o-x-y_-c-o-n-f-i-g.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/index.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService/ProxyVpnService/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/-proxy-vpn-service.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService/networkManager/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/network-manager.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService/onCreate/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/on-create.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService/onDestroy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/on-destroy.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService/onStartCommand/#android.content.Intent?#kotlin.Int#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/on-start-command.html
$dokka.location:com.android.proxy_self.infrastructure.services/ProxyVpnService/proxyManager/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.services/-proxy-vpn-service/proxy-manager.html
$dokka.location:com.android.proxy_self.infrastructure.utils////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/DATABASE/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-d-a-t-a-b-a-s-e.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/DOMAIN_EXTRACTOR/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-d-o-m-a-i-n_-e-x-t-r-a-c-t-o-r.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/ENCRYPTION/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-e-n-c-r-y-p-t-i-o-n.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/MANAGER/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-m-a-n-a-g-e-r.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/NAVIGATION/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-n-a-v-i-g-a-t-i-o-n.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/NETWORK/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-n-e-t-w-o-r-k.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/NOTIFICATION/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-n-o-t-i-f-i-c-a-t-i-o-n.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/RECEIVER/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-r-e-c-e-i-v-e-r.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/REPOSITORY/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-r-e-p-o-s-i-t-o-r-y.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/SERVICE/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-s-e-r-v-i-c-e.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/SOCKS5/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-s-o-c-k-s5.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/TILE/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-t-i-l-e.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/UI/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-u-i.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/USE_CASE/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-u-s-e_-c-a-s-e.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/UTILS/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-u-t-i-l-s.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/VALIDATION/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-v-a-l-i-d-a-t-i-o-n.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/VIEWMODEL/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-v-i-e-w-m-o-d-e-l.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/VPN/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-v-p-n.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger.Tags/WORKER/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/-tags/-w-o-r-k-e-r.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/configChange/#kotlin.String#kotlin.String#kotlin.Any?#kotlin.Any?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/config-change.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/d/#kotlin.String#kotlin.String#kotlin.Throwable?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/d.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/databaseOperation/#kotlin.String#kotlin.String#kotlin.String#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/database-operation.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/e/#kotlin.String#kotlin.String#kotlin.Throwable?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/e.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/i/#kotlin.String#kotlin.String#kotlin.Throwable?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/i.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/measureTime/#kotlin.String#kotlin.String#kotlin.Function0[TypeParam(bounds=[kotlin.Any?])]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/measure-time.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/methodEntry/#kotlin.String#kotlin.String#kotlin.collections.Map[kotlin.String,kotlin.Any?]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/method-entry.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/methodExit/#kotlin.String#kotlin.String#kotlin.Any?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/method-exit.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/networkRequest/#kotlin.String#kotlin.String#kotlin.String#kotlin.collections.Map[kotlin.String,kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/network-request.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/networkResponse/#kotlin.String#kotlin.Int#kotlin.String#kotlin.Long/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/network-response.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/performance/#kotlin.String#kotlin.String#kotlin.Number#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/performance.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/security/#kotlin.String#kotlin.String#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/security.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/stateChange/#kotlin.String#kotlin.Any?#kotlin.Any?#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/state-change.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/userAction/#kotlin.String#kotlin.String#kotlin.collections.Map[kotlin.String,kotlin.Any?]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/user-action.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/v/#kotlin.String#kotlin.String#kotlin.Throwable?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/v.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DebugLogger/w/#kotlin.String#kotlin.String#kotlin.Throwable?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-debug-logger/w.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DomainExtractor///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-domain-extractor/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/DomainExtractor/extractDomain/#java.nio.ByteBuffer/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-domain-extractor/extract-domain.html
$dokka.location:com.android.proxy_self.infrastructure.utils/EncryptionUtils///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/EncryptionUtils/EncryptionUtils/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/-encryption-utils.html
$dokka.location:com.android.proxy_self.infrastructure.utils/EncryptionUtils/clearAllEncryptedData/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/clear-all-encrypted-data.html
$dokka.location:com.android.proxy_self.infrastructure.utils/EncryptionUtils/decrypt/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/decrypt.html
$dokka.location:com.android.proxy_self.infrastructure.utils/EncryptionUtils/encrypt/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/encrypt.html
$dokka.location:com.android.proxy_self.infrastructure.utils/EncryptionUtils/isEncryptionAvailable/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-encryption-utils/is-encryption-available.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo/NetworkInfo/#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/-network-info.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo/isAvailable/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-available.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo/isCellular/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-cellular.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo/isMetered/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-metered.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo/isWifi/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/is-wifi.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkInfo/type/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-info/type.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/NetworkUtils/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/-network-utils.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/extractDomainFromUrl/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/extract-domain-from-url.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/getNetworkInfo/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/get-network-info.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/getNetworkType/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/get-network-type.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/isCellularConnected/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-cellular-connected.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/isDomainInWhitelist/#kotlin.String#kotlin.collections.List[kotlin.String]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-domain-in-whitelist.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/isHostReachable/#kotlin.String#kotlin.Int#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-host-reachable.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/isMeteredConnection/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-metered-connection.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/isNetworkAvailable/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-network-available.html
$dokka.location:com.android.proxy_self.infrastructure.utils/NetworkUtils/isWifiConnected/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-network-utils/is-wifi-connected.html
$dokka.location:com.android.proxy_self.infrastructure.utils/Socks5Client.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-socks5-client/-companion/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/Socks5Client///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-socks5-client/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/Socks5Client/Socks5Client/#kotlin.String#kotlin.Int#kotlin.String?#kotlin.String?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-socks5-client/-socks5-client.html
$dokka.location:com.android.proxy_self.infrastructure.utils/Socks5Client/connect/#kotlin.String#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-socks5-client/connect.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult.Error///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-error/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult.Error/Error/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-error/-error.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult.Error/message/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-error/message.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult.Success///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-success/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult.Success/Success/#TypeParam(bounds=[kotlin.Any?])/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-success/-success.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult.Success/data/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/-success/data.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationResult///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-result/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/-companion/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/index.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/ValidationUtils/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/-validation-utils.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/getDomainsValidationError/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-domains-validation-error.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/getIpValidationError/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-ip-validation-error.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/getPasswordValidationError/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-password-validation-error.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/getPortValidationError/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-port-validation-error.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/getUsernameValidationError/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/get-username-validation-error.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidDomain/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-domain.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidDomainList/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-domain-list.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidIpAddress/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-ip-address.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidIpv4Address/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-ipv4-address.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidIpv6Address/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-ipv6-address.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidPassword/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-password.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidPort/#kotlin.Int/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-port.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidPort/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-port.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/isValidUsername/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/is-valid-username.html
$dokka.location:com.android.proxy_self.infrastructure.utils/ValidationUtils/parseAndValidateDomains/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/-validation-utils/parse-and-validate-domains.html
$dokka.location:com.android.proxy_self.infrastructure.workers////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/index.html
$dokka.location:com.android.proxy_self.infrastructure.workers/AutoStartProxyWorker///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/-auto-start-proxy-worker/index.html
$dokka.location:com.android.proxy_self.infrastructure.workers/AutoStartProxyWorker/AutoStartProxyWorker/#android.content.Context#androidx.work.WorkerParameters#com.android.proxy_self.domain.usecases.GetProxyConfigUseCase/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/-auto-start-proxy-worker/-auto-start-proxy-worker.html
$dokka.location:com.android.proxy_self.infrastructure.workers/AutoStartProxyWorker/doWork/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/-auto-start-proxy-worker/do-work.html
$dokka.location:com.android.proxy_self.presentation.quicksettings////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/index.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/-companion/index.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile.Companion/requestTileUpdate/#android.content.Context/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/-companion/request-tile-update.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/index.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/ProxyQuickSettingsTile/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/-proxy-quick-settings-tile.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/getProxyConfigUseCase/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/get-proxy-config-use-case.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/onClick/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/on-click.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/onStartListening/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/on-start-listening.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/startProxyUseCase/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/start-proxy-use-case.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/stopProxyUseCase/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/stop-proxy-use-case.html
$dokka.location:com.android.proxy_self.presentation.quicksettings/ProxyQuickSettingsTile/updateTileFromService/#kotlin.Boolean#com.android.proxy_self.domain.entities.ProxyStatus/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/-proxy-quick-settings-tile/update-tile-from-service.html
$dokka.location:com.android.proxy_self.presentation.state////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/index.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/index.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/AboutUiState/#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.collections.List[com.android.proxy_self.presentation.state.LibraryInfo]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/-about-ui-state.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/appName/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/app-name.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/appVersion/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/app-version.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/buildDate/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/build-date.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/buildNumber/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/build-number.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/developerEmail/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/developer-email.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/developerName/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/developer-name.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/githubUrl/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/github-url.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/licenseUrl/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/license-url.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/openSourceLibraries/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/open-source-libraries.html
$dokka.location:com.android.proxy_self.presentation.state/AboutUiState/privacyPolicyUrl/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-about-ui-state/privacy-policy-url.html
$dokka.location:com.android.proxy_self.presentation.state/ConnectionTestResult.Failure///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-failure/index.html
$dokka.location:com.android.proxy_self.presentation.state/ConnectionTestResult.Failure/Failure/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-failure/-failure.html
$dokka.location:com.android.proxy_self.presentation.state/ConnectionTestResult.Failure/error/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-failure/error.html
$dokka.location:com.android.proxy_self.presentation.state/ConnectionTestResult.Success///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/-success/index.html
$dokka.location:com.android.proxy_self.presentation.state/ConnectionTestResult///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-connection-test-result/index.html
$dokka.location:com.android.proxy_self.presentation.state/LibraryInfo///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/index.html
$dokka.location:com.android.proxy_self.presentation.state/LibraryInfo/LibraryInfo/#kotlin.String#kotlin.String#kotlin.String#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/-library-info.html
$dokka.location:com.android.proxy_self.presentation.state/LibraryInfo/license/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/license.html
$dokka.location:com.android.proxy_self.presentation.state/LibraryInfo/name/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/name.html
$dokka.location:com.android.proxy_self.presentation.state/LibraryInfo/url/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/url.html
$dokka.location:com.android.proxy_self.presentation.state/LibraryInfo/version/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-library-info/version.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/index.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/ProxyUiState/#com.android.proxy_self.domain.entities.ProxyConfig#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#com.android.proxy_self.domain.entities.ProxyStatusInfo?#kotlin.String?#kotlin.collections.Map[kotlin.String,kotlin.String]#kotlin.String#kotlin.String#kotlin.String#kotlin.String#kotlin.String#com.android.proxy_self.domain.entities.ProxyType#kotlin.Boolean#kotlin.String?#com.android.proxy_self.presentation.state.ConnectionTestResult?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/-proxy-ui-state.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/connectionTestResult/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/connection-test-result.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/domains/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/domains.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/error/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/error.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/getDomainsList/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/get-domains-list.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/hasValidationErrors/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/has-validation-errors.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isAnyOperationInProgress/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-any-operation-in-progress.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isFormValid/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-form-valid.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isLoading/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-loading.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isPasswordVisible/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-password-visible.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isProxyEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-proxy-enabled.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isSaving/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-saving.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/isTestingConnection/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/is-testing-connection.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/password/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/password.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/proxyConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/proxy-config.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/proxyStatus/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/proxy-status.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/selectedProxyType/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/selected-proxy-type.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/serverAddress/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/server-address.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/serverPort/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/server-port.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/successMessage/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/success-message.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/toProxyConfig/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/to-proxy-config.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/username/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/username.html
$dokka.location:com.android.proxy_self.presentation.state/ProxyUiState/validationErrors/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-proxy-ui-state/validation-errors.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/index.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/SettingsUiState/#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.String?#kotlin.String?#kotlin.Boolean#kotlin.Boolean#kotlin.Boolean#kotlin.String#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/-settings-ui-state.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/appVersion/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/app-version.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/buildNumber/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/build-number.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/error/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/error.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isAnyOperationInProgress/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-any-operation-in-progress.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isAutoStartEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-auto-start-enabled.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isClearing/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-clearing.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isDarkThemeEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-dark-theme-enabled.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isExporting/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-exporting.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isImporting/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-importing.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isLoading/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-loading.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isNotificationsEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-notifications-enabled.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/isSaving/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/is-saving.html
$dokka.location:com.android.proxy_self.presentation.state/SettingsUiState/message/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.state/-settings-ui-state/message.html
$dokka.location:com.android.proxy_self.presentation.ui.components////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/index.html
$dokka.location:com.android.proxy_self.presentation.ui.components//LoadingButton/#kotlin.Function0[kotlin.Unit]#kotlin.String#androidx.compose.ui.Modifier#kotlin.Boolean#kotlin.Boolean#com.android.proxy_self.presentation.ui.components.ButtonVariant/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-loading-button.html
$dokka.location:com.android.proxy_self.presentation.ui.components//ProxyConfigForm/#com.android.proxy_self.presentation.state.ProxyUiState#kotlin.Function1[kotlin.String,kotlin.Unit]#kotlin.Function1[kotlin.String,kotlin.Unit]#kotlin.Function1[kotlin.String,kotlin.Unit]#kotlin.Function1[kotlin.String,kotlin.Unit]#kotlin.Function1[kotlin.String,kotlin.Unit]#kotlin.Function1[com.android.proxy_self.domain.entities.ProxyType,kotlin.Unit]#kotlin.Function0[kotlin.Unit]#androidx.compose.ui.Modifier/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-proxy-config-form.html
$dokka.location:com.android.proxy_self.presentation.ui.components//ProxyStatusCard/#com.android.proxy_self.domain.entities.ProxyStatusInfo?#kotlin.Boolean#androidx.compose.ui.Modifier/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-proxy-status-card.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant.Filled///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/-filled/index.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant.Outlined///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/-outlined/index.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant.Text///PointingToDeclaration/{"org.jetbrains.dokka.links.EnumEntryDRIExtra":{"key":"org.jetbrains.dokka.links.EnumEntryDRIExtra"}}-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/-text/index.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/index.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant/entries/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/entries.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant/valueOf/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/value-of.html
$dokka.location:com.android.proxy_self.presentation.ui.components/ButtonVariant/values/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/-button-variant/values.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/index.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation//ProxyNavigation/#androidx.navigation.NavHostController/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-proxy-navigation.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation/Screen.About///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/-about/index.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation/Screen.ProxyConfig///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/-proxy-config/index.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation/Screen.Settings///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/-settings/index.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation/Screen///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/index.html
$dokka.location:com.android.proxy_self.presentation.ui.navigation/Screen/route/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/-screen/route.html
$dokka.location:com.android.proxy_self.presentation.ui.screens////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/index.html
$dokka.location:com.android.proxy_self.presentation.ui.screens//AboutScreen/#kotlin.Function0[kotlin.Unit]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/-about-screen.html
$dokka.location:com.android.proxy_self.presentation.ui.screens//ProxyConfigScreen/#kotlin.Function0[kotlin.Unit]#kotlin.Function0[kotlin.Unit]#com.android.proxy_self.presentation.viewmodel.ProxyViewModel/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/-proxy-config-screen.html
$dokka.location:com.android.proxy_self.presentation.ui.screens//SettingsScreen/#kotlin.Function0[kotlin.Unit]#kotlin.Function0[kotlin.Unit]#com.android.proxy_self.presentation.viewmodel.SettingsViewModel/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/-settings-screen.html
$dokka.location:com.android.proxy_self.presentation.ui.theme////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/index.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//OutlineDark/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-outline-dark.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//OutlineLight/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-outline-light.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//Pink40/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-pink40.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//Pink80/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-pink80.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//ProxyBlue/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-blue.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//ProxyGreen/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-green.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//ProxyOrange/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-orange.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//ProxyRed/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-red.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//ProxySelfTheme/#kotlin.Boolean#kotlin.Boolean#kotlin.Function0[kotlin.Unit]/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-proxy-self-theme.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//Purple40/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple40.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//Purple80/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple80.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//PurpleGrey40/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple-grey40.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//PurpleGrey80/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-purple-grey80.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//StatusConnected/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-connected.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//StatusConnecting/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-connecting.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//StatusDisconnected/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-disconnected.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//StatusError/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-status-error.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//SurfaceVariantDark/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-surface-variant-dark.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//SurfaceVariantLight/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-surface-variant-light.html
$dokka.location:com.android.proxy_self.presentation.ui.theme//Typography/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/-typography.html
$dokka.location:com.android.proxy_self.presentation.viewmodel////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/index.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/index.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/ProxyViewModel/#com.android.proxy_self.domain.usecases.StartProxyUseCase#com.android.proxy_self.domain.usecases.StopProxyUseCase#com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase#com.android.proxy_self.domain.usecases.GetProxyConfigUseCase#com.android.proxy_self.domain.usecases.TestProxyConnectionUseCase#com.android.proxy_self.infrastructure.utils.ValidationUtils/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/-proxy-view-model.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/clearConnectionTestResult/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/clear-connection-test-result.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/clearError/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/clear-error.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/clearSuccessMessage/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/clear-success-message.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/saveConfiguration/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/save-configuration.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/startProxy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/start-proxy.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/stopProxy/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/stop-proxy.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/testConnection/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/test-connection.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/togglePasswordVisibility/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/toggle-password-visibility.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/uiState/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/ui-state.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/updateDomains/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-domains.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/updatePassword/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-password.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/updateProxyType/#com.android.proxy_self.domain.entities.ProxyType/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-proxy-type.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/updateServerAddress/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-server-address.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/updateServerPort/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-server-port.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/ProxyViewModel/updateUsername/#kotlin.String/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-proxy-view-model/update-username.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel.Companion///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/-companion/index.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/index.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/SettingsViewModel/#android.content.Context#com.android.proxy_self.domain.usecases.GetProxyConfigUseCase#com.android.proxy_self.domain.usecases.SaveProxyConfigUseCase/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/-settings-view-model.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/clearAllData/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/clear-all-data.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/clearError/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/clear-error.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/clearMessage/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/clear-message.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/exportConfiguration/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/export-configuration.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/importConfiguration/#kotlin.String?/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/import-configuration.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/isAutoStartEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/is-auto-start-enabled.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/isDarkThemeEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/is-dark-theme-enabled.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/isNotificationsEnabled/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/is-notifications-enabled.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/toggleAutoStart/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/toggle-auto-start.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/toggleDarkTheme/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/toggle-dark-theme.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/toggleNotifications/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/toggle-notifications.html
$dokka.location:com.android.proxy_self.presentation.viewmodel/SettingsViewModel/uiState/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/-settings-view-model/ui-state.html
$dokka.location:com.android.proxy_self////PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/index.html
$dokka.location:com.android.proxy_self/MainActivity///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-main-activity/index.html
$dokka.location:com.android.proxy_self/MainActivity/MainActivity/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-main-activity/-main-activity.html
$dokka.location:com.android.proxy_self/ProxyApplication///PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-proxy-application/index.html
$dokka.location:com.android.proxy_self/ProxyApplication/ProxyApplication/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-proxy-application/-proxy-application.html
$dokka.location:com.android.proxy_self/ProxyApplication/onCreate/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-proxy-application/on-create.html
$dokka.location:com.android.proxy_self/ProxyApplication/workManagerConfiguration/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-proxy-application/work-manager-configuration.html
$dokka.location:com.android.proxy_self/ProxyApplication/workerFactory/#/PointingToDeclaration/-proxy -self -android -app/com.android.proxy_self/-proxy-application/worker-factory.html
com.android.proxy_self
com.android.proxy_self.data.datasources.local
com.android.proxy_self.data.datasources.remote
com.android.proxy_self.data.mappers
com.android.proxy_self.data.repositories
com.android.proxy_self.di
com.android.proxy_self.domain.entities
com.android.proxy_self.domain.repositories
com.android.proxy_self.domain.usecases
com.android.proxy_self.domain.usecases.base
com.android.proxy_self.infrastructure.managers
com.android.proxy_self.infrastructure.receivers
com.android.proxy_self.infrastructure.services
com.android.proxy_self.infrastructure.utils
com.android.proxy_self.infrastructure.workers
com.android.proxy_self.presentation.quicksettings
com.android.proxy_self.presentation.state
com.android.proxy_self.presentation.ui.components
com.android.proxy_self.presentation.ui.navigation
com.android.proxy_self.presentation.ui.screens
com.android.proxy_self.presentation.ui.theme
com.android.proxy_self.presentation.viewmodel
