<!DOCTYPE html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>ProxyViewModel</title>
    <link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "../../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async="async"></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="../../../scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/prism.js" async="async"></script>
<script type="text/javascript" src="../../../ui-kit/ui-kit.min.js" defer="defer"></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
</head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
            <a class="library-name--link" href="../../../index.html">
                    Proxy Self Android App
            </a>
        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle"
                type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
1.0.0        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active=""
                        data-filter=":app:dokkaHtml/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox"
                        data-role="dropdown-toggle"
                        aria-controls="platform-tags-listbox"
                        aria-haspopup="listbox"
                        aria-expanded="false"
                        aria-label="Toggle source sets"
                ></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":app:dokkaHtml/release"
                                       data-filter=":app:dokkaHtml/release"/>
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button"
                    type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list"
                        data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    Proxy Self Android App
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageIds="Proxy Self Android App::com.android.proxy_self.presentation.viewmodel/ProxyViewModel///PointingToDeclaration//492899073">
  <div class="breadcrumbs"><a href="../../../index.html">Proxy Self Android App</a><span class="delimiter">/</span><a href="../index.html">com.android.proxy_self.presentation.viewmodel</a><span class="delimiter">/</span><span class="current">ProxyViewModel</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Proxy</span><wbr></wbr><span>View</span><wbr></wbr><span><span>Model</span></span></h1>
    <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">class </span><a href="index.html">ProxyViewModel</a> <span><span class="token annotation builtin">@</span><span data-unresolved-link="javax.inject/Inject///PointingToDeclaration/"><span class="token annotation builtin">Inject</span></span> </span><span class="token keyword">constructor</span><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">startProxyUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-start-proxy-use-case/index.html">StartProxyUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">stopProxyUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-stop-proxy-use-case/index.html">StopProxyUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">saveProxyConfigUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/index.html">SaveProxyConfigUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">getProxyConfigUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/index.html">GetProxyConfigUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">testProxyConnectionUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/index.html">TestProxyConnectionUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">validationUtils<span class="token operator">: </span><a href="../../com.android.proxy_self.infrastructure.utils/-validation-utils/index.html">ValidationUtils</a></span></span><span class="token punctuation">)</span> : <a href="https://developer.android.com/reference/kotlin/androidx/lifecycle/ViewModel.html">ViewModel</a></div><p class="paragraph">ViewModel for proxy configuration screen Implements MVVM pattern with proper state management Follows Single Responsibility Principle</p></div></div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button></div>
    <div class="tabs-section-body">
      <div data-togglable="CONSTRUCTOR">
        <h2 class="">Constructors</h2>
        <div class="table"><a data-name="1901546287%2FConstructors%2F492899073" anchor-label="ProxyViewModel" id="1901546287%2FConstructors%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-togglable="CONSTRUCTOR" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-proxy-view-model.html"><span>Proxy</span><wbr></wbr><span>View</span><wbr></wbr><span><span>Model</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1901546287%2FConstructors%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><div class="block"><div class="block"><span class="token annotation builtin">@</span><span data-unresolved-link="javax.inject/Inject///PointingToDeclaration/"><span class="token annotation builtin">Inject</span></span></div></div><span class="token keyword">constructor</span><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">startProxyUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-start-proxy-use-case/index.html">StartProxyUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">stopProxyUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-stop-proxy-use-case/index.html">StopProxyUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">saveProxyConfigUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-save-proxy-config-use-case/index.html">SaveProxyConfigUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">getProxyConfigUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-get-proxy-config-use-case/index.html">GetProxyConfigUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">testProxyConnectionUseCase<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.usecases/-test-proxy-connection-use-case/index.html">TestProxyConnectionUseCase</a><span class="token punctuation">, </span></span><span class="parameter ">validationUtils<span class="token operator">: </span><a href="../../com.android.proxy_self.infrastructure.utils/-validation-utils/index.html">ValidationUtils</a></span></span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="1945485439%2FProperties%2F492899073" anchor-label="uiState" id="1945485439%2FProperties%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="ui-state.html"><span>ui</span><wbr></wbr><span><span>State</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1945485439%2FProperties%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">val </span><a href="ui-state.html">uiState</a><span class="token operator">: </span><span data-unresolved-link="kotlinx.coroutines.flow/StateFlow///PointingToDeclaration/">StateFlow</span><span class="token operator">&lt;</span><a href="../../com.android.proxy_self.presentation.state/-proxy-ui-state/index.html">ProxyUiState</a><span class="token operator">&gt;</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="978795377%2FFunctions%2F492899073" anchor-label="addCloseable" id="978795377%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../-settings-view-model/index.html#383812252%2FFunctions%2F492899073"><span>add</span><wbr></wbr><span><span>Closeable</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="978795377%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">open </span><span class="token keyword">fun </span><a href="../-settings-view-model/index.html#383812252%2FFunctions%2F492899073"><span class="token function">addCloseable</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">closeable<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-auto-closeable/index.html">AutoCloseable</a></span></span><span class="token punctuation">)</span></div><div class="symbol monospace"><span class="token keyword">fun </span><a href="../-settings-view-model/index.html#1722490497%2FFunctions%2F492899073"><span class="token function">addCloseable</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">key<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a><span class="token punctuation">, </span></span><span class="parameter ">closeable<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-auto-closeable/index.html">AutoCloseable</a></span></span><span class="token punctuation">)</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1671376638%2FFunctions%2F492899073" anchor-label="clearConnectionTestResult" id="-1671376638%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="clear-connection-test-result.html"><span>clear</span><wbr></wbr><span>Connection</span><wbr></wbr><span>Test</span><wbr></wbr><span><span>Result</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1671376638%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="clear-connection-test-result.html"><span class="token function">clearConnectionTestResult</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Clear connection test result</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1083668355%2FFunctions%2F492899073" anchor-label="clearError" id="1083668355%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="clear-error.html"><span>clear</span><wbr></wbr><span><span>Error</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1083668355%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="clear-error.html"><span class="token function">clearError</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Clear error message</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1949885269%2FFunctions%2F492899073" anchor-label="clearSuccessMessage" id="-1949885269%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="clear-success-message.html"><span>clear</span><wbr></wbr><span>Success</span><wbr></wbr><span><span>Message</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1949885269%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="clear-success-message.html"><span class="token function">clearSuccessMessage</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Clear success message</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1102255800%2FFunctions%2F492899073" anchor-label="getCloseable" id="1102255800%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../-settings-view-model/index.html#1102255800%2FFunctions%2F492899073"><span>get</span><wbr></wbr><span><span>Closeable</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1102255800%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><span class="token operator">&lt;</span><a href="../-settings-view-model/index.html#1102255800%2FFunctions%2F492899073">T</a><span class="token operator"> : </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-auto-closeable/index.html">AutoCloseable</a><span class="token operator">&gt; </span><a href="../-settings-view-model/index.html#1102255800%2FFunctions%2F492899073"><span class="token function">getCloseable</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">key<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="../-settings-view-model/index.html#1102255800%2FFunctions%2F492899073">T</a><span class="token operator">?</span></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1520357667%2FFunctions%2F492899073" anchor-label="saveConfiguration" id="1520357667%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="save-configuration.html"><span>save</span><wbr></wbr><span><span>Configuration</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1520357667%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="save-configuration.html"><span class="token function">saveConfiguration</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Save proxy configuration</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="445356338%2FFunctions%2F492899073" anchor-label="startProxy" id="445356338%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="start-proxy.html"><span>start</span><wbr></wbr><span><span>Proxy</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="445356338%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="start-proxy.html"><span class="token function">startProxy</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Start proxy service</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1403634064%2FFunctions%2F492899073" anchor-label="stopProxy" id="-1403634064%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="stop-proxy.html"><span>stop</span><wbr></wbr><span><span>Proxy</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1403634064%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="stop-proxy.html"><span class="token function">stopProxy</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Stop proxy service</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="714455886%2FFunctions%2F492899073" anchor-label="testConnection" id="714455886%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="test-connection.html"><span>test</span><wbr></wbr><span><span>Connection</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="714455886%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="test-connection.html"><span class="token function">testConnection</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Test proxy connection</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1057814083%2FFunctions%2F492899073" anchor-label="togglePasswordVisibility" id="-1057814083%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="toggle-password-visibility.html"><span>toggle</span><wbr></wbr><span>Password</span><wbr></wbr><span><span>Visibility</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1057814083%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="toggle-password-visibility.html"><span class="token function">togglePasswordVisibility</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Toggle password visibility</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1211865042%2FFunctions%2F492899073" anchor-label="updateDomains" id="-1211865042%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="update-domains.html"><span>update</span><wbr></wbr><span><span>Domains</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1211865042%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="update-domains.html"><span class="token function">updateDomains</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">domains<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Update domains</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1011473130%2FFunctions%2F492899073" anchor-label="updatePassword" id="1011473130%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="update-password.html"><span>update</span><wbr></wbr><span><span>Password</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1011473130%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="update-password.html"><span class="token function">updatePassword</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">password<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Update password</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1423426749%2FFunctions%2F492899073" anchor-label="updateProxyType" id="1423426749%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="update-proxy-type.html"><span>update</span><wbr></wbr><span>Proxy</span><wbr></wbr><span><span>Type</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1423426749%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="update-proxy-type.html"><span class="token function">updateProxyType</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">proxyType<span class="token operator">: </span><a href="../../com.android.proxy_self.domain.entities/-proxy-type/index.html">ProxyType</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Update proxy type</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-50089040%2FFunctions%2F492899073" anchor-label="updateServerAddress" id="-50089040%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="update-server-address.html"><span>update</span><wbr></wbr><span>Server</span><wbr></wbr><span><span>Address</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-50089040%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="update-server-address.html"><span class="token function">updateServerAddress</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">address<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Update server address</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="2037742067%2FFunctions%2F492899073" anchor-label="updateServerPort" id="2037742067%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="update-server-port.html"><span>update</span><wbr></wbr><span>Server</span><wbr></wbr><span><span>Port</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2037742067%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="update-server-port.html"><span class="token function">updateServerPort</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">port<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Update server port</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1322788069%2FFunctions%2F492899073" anchor-label="updateUsername" id="1322788069%2FFunctions%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
          <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="update-username.html"><span>update</span><wbr></wbr><span><span>Username</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1322788069%2FFunctions%2F492899073"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":app:dokkaHtml/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="update-username.html"><span class="token function">updateUsername</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">username<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span></div><div class="brief "><p class="paragraph">Update username</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>