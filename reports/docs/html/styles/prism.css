/*
 * Custom Dokka styles
 */
code .token {
    white-space: pre;
}

/**
 * Styles based on webhelp's prism.js styles
 * Changes:
 * - Since webhelp's styles are in .pcss, they use nesting which is not achievable in native CSS
 *   so nested css blocks have been unrolled (like dark theme).
 * - Webhelp uses "Custom Class" prism.js plugin, so all of their prism classes are prefixed with "--prism".
 *   Dokka doesn't seem to need this plugin at the moment, so all "--prism" prefixes have been removed.
 * - Removed all styles related to `pre` and `code` tags. Kotlinlang's resulting styles are so spread out and complicated
 *   that it's difficult to gather in one place. Instead use code styles defined in the main Dokka styles,
 *   which at the moment looks fairly similar.
 *
 * Based on prism.js default theme
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
	color: #8c8c8c;
}

.token.punctuation {
	color: #999;
}

.token.namespace {
	opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
	color: #871094;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
	color: #067d17;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
	color: #9a6e3a;
	/* This background color was intended by the author of this theme. */
	background: hsla(0, 0%, 100%, 0.5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
	font-size: inherit; /* to override .keyword */
	color: #0033b3;
}

.token.function {
	color: #00627a;
}

.token.class-name {
	color: #000000;
}

.token.regex,
.token.important,
.token.variable {
	color: #871094;
}

.token.important,
.token.bold {
	font-weight: bold;
}
.token.italic {
	font-style: italic;
}

.token.entity {
	cursor: help;
}

.token.operator {
	background: none;
}

/*
 * DARK THEME
 */
:root.theme-dark .token.comment,
:root.theme-dark .token.prolog,
:root.theme-dark .token.cdata {
	color: #808080;
}

:root.theme-dark .token.delimiter,
:root.theme-dark .token.boolean,
:root.theme-dark .token.keyword,
:root.theme-dark .token.selector,
:root.theme-dark .token.important,
:root.theme-dark .token.atrule {
	color: #cc7832;
}

:root.theme-dark .token.operator,
:root.theme-dark .token.punctuation,
:root.theme-dark .token.attr-name {
	color: #a9b7c6;
}

:root.theme-dark .token.tag,
:root.theme-dark .token.tag .punctuation,
:root.theme-dark .token.doctype,
:root.theme-dark .token.builtin {
	color: #e8bf6a;
}

:root.theme-dark .token.entity,
:root.theme-dark .token.number,
:root.theme-dark .token.symbol {
	color: #6897bb;
}

:root.theme-dark .token.property,
:root.theme-dark .token.constant,
:root.theme-dark .token.variable {
	color: #9876aa;
}

:root.theme-dark .token.string,
:root.theme-dark .token.char {
	color: #6a8759;
}

:root.theme-dark .token.attr-value,
:root.theme-dark .token.attr-value .punctuation {
	color: #a5c261;
}

:root.theme-dark .token.attr-value .punctuation:first-child {
	color: #a9b7c6;
}

:root.theme-dark .token.url {
	text-decoration: underline;

	color: #287bde;
	background: transparent;
}

:root.theme-dark .token.function {
	color: #ffc66d;
}

:root.theme-dark .token.regex {
	background: #364135;
}

:root.theme-dark .token.deleted {
	background: #484a4a;
}

:root.theme-dark .token.inserted {
	background: #294436;
}

:root.theme-dark .token.class-name {
	color: #a9b7c6;
}

:root.theme-dark .token.function {
	color: #ffc66d;
}

:root.theme-darkcode .language-css .token.property,
:root.theme-darkcode .language-css,
:root.theme-dark .token.property + .token.punctuation {
	color: #a9b7c6;
}

code.language-css .token.id {
	color: #ffc66d;
}

:root.theme-dark code.language-css .token.selector > .token.class,
:root.theme-dark code.language-css .token.selector > .token.attribute,
:root.theme-dark code.language-css .token.selector > .token.pseudo-class,
:root.theme-dark code.language-css .token.selector > .token.pseudo-element {
	color: #ffc66d;
}

:root.theme-dark .language-plaintext .token {
	/* plaintext code should be colored as article text */
	color: inherit !important;
}
