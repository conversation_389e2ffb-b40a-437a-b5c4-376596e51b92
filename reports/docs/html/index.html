<!DOCTYPE html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>Proxy Self Android App</title>
    <link href="images/logo-icon.svg" rel="icon" type="image/svg">
    <script>var pathToRoot = "";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async="async"></script>
<script type="text/javascript" src="scripts/sourceset_dependencies.js" async="async"></script>
<link href="styles/style.css" rel="Stylesheet">
<link href="styles/main.css" rel="Stylesheet">
<link href="styles/prism.css" rel="Stylesheet">
<link href="styles/logo-styles.css" rel="Stylesheet">
<link href="styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="scripts/clipboard.js" async="async"></script>
<script type="text/javascript" src="scripts/navigation-loader.js" async="async"></script>
<script type="text/javascript" src="scripts/platform-content-handler.js" async="async"></script>
<script type="text/javascript" src="scripts/main.js" defer="defer"></script>
<script type="text/javascript" src="scripts/prism.js" async="async"></script>
<script type="text/javascript" src="ui-kit/ui-kit.min.js" defer="defer"></script>
<script type="text/javascript" src="scripts/symbol-parameters-wrapper_deferred.js" defer="defer"></script>
</head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
            <a class="library-name--link" href="index.html">
                    Proxy Self Android App
            </a>
        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle"
                type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
1.0.0        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active=""
                        data-filter=":app:dokkaHtml/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox"
                        data-role="dropdown-toggle"
                        aria-controls="platform-tags-listbox"
                        aria-haspopup="listbox"
                        aria-expanded="false"
                        aria-label="Toggle source sets"
                ></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":app:dokkaHtml/release"
                                       data-filter=":app:dokkaHtml/release"/>
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button"
                    type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list"
                        data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    Proxy Self Android App
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" id="content" pageIds="Proxy Self Android App::////PointingToDeclaration//492899073">
  <div class="breadcrumbs"></div>
  <div class="cover ">
    <h1 class="cover"><span><span>Proxy</span></span> <span><span>Self</span></span> <span><span>Android</span></span> <span><span>App</span></span></h1>
  </div>
  <h2 class="">Packages</h2>
  <div class="table"><a data-name="-2027930485%2FPackages%2F492899073" anchor-label="com.android.proxy_self" id="-2027930485%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self/index.html">com.android.proxy_self</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-2027930485%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-1583770226%2FPackages%2F492899073" anchor-label="com.android.proxy_self.data.datasources.local" id="-1583770226%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.data.datasources.local/index.html">com.android.proxy_self.data.datasources.local</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1583770226%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1439819975%2FPackages%2F492899073" anchor-label="com.android.proxy_self.data.datasources.remote" id="1439819975%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.data.datasources.remote/index.html">com.android.proxy_self.data.datasources.remote</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1439819975%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-189069291%2FPackages%2F492899073" anchor-label="com.android.proxy_self.data.mappers" id="-189069291%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.data.mappers/index.html">com.android.proxy_self.data.mappers</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-189069291%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="324015497%2FPackages%2F492899073" anchor-label="com.android.proxy_self.data.repositories" id="324015497%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.data.repositories/index.html">com.android.proxy_self.data.repositories</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="324015497%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-20400116%2FPackages%2F492899073" anchor-label="com.android.proxy_self.di" id="-20400116%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.di/index.html">com.android.proxy_self.di</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-20400116%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-377894904%2FPackages%2F492899073" anchor-label="com.android.proxy_self.domain.entities" id="-377894904%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.domain.entities/index.html">com.android.proxy_self.domain.entities</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-377894904%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1214338159%2FPackages%2F492899073" anchor-label="com.android.proxy_self.domain.repositories" id="1214338159%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.domain.repositories/index.html">com.android.proxy_self.domain.repositories</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1214338159%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1219655331%2FPackages%2F492899073" anchor-label="com.android.proxy_self.domain.usecases" id="1219655331%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.domain.usecases/index.html">com.android.proxy_self.domain.usecases</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1219655331%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1811766016%2FPackages%2F492899073" anchor-label="com.android.proxy_self.domain.usecases.base" id="1811766016%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.domain.usecases.base/index.html">com.android.proxy_self.domain.usecases.base</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1811766016%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1813000750%2FPackages%2F492899073" anchor-label="com.android.proxy_self.infrastructure.managers" id="1813000750%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.infrastructure.managers/index.html">com.android.proxy_self.infrastructure.managers</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1813000750%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-1483817312%2FPackages%2F492899073" anchor-label="com.android.proxy_self.infrastructure.receivers" id="-1483817312%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.infrastructure.receivers/index.html">com.android.proxy_self.infrastructure.receivers</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1483817312%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="505632294%2FPackages%2F492899073" anchor-label="com.android.proxy_self.infrastructure.services" id="505632294%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.infrastructure.services/index.html">com.android.proxy_self.infrastructure.services</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="505632294%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1794687405%2FPackages%2F492899073" anchor-label="com.android.proxy_self.infrastructure.utils" id="1794687405%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.infrastructure.utils/index.html">com.android.proxy_self.infrastructure.utils</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1794687405%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1240703537%2FPackages%2F492899073" anchor-label="com.android.proxy_self.infrastructure.workers" id="1240703537%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.infrastructure.workers/index.html">com.android.proxy_self.infrastructure.workers</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1240703537%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="2144792323%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.quicksettings" id="2144792323%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.quicksettings/index.html">com.android.proxy_self.presentation.quicksettings</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="2144792323%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1820680260%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.state" id="1820680260%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.state/index.html">com.android.proxy_self.presentation.state</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1820680260%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="851177635%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.ui.components" id="851177635%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.ui.components/index.html">com.android.proxy_self.presentation.ui.components</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="851177635%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1583388193%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.ui.navigation" id="1583388193%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.ui.navigation/index.html">com.android.proxy_self.presentation.ui.navigation</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1583388193%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-328810658%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.ui.screens" id="-328810658%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.ui.screens/index.html">com.android.proxy_self.presentation.ui.screens</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-328810658%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="1478730336%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.ui.theme" id="1478730336%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.ui.theme/index.html">com.android.proxy_self.presentation.ui.theme</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1478730336%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
<a data-name="-699381481%2FPackages%2F492899073" anchor-label="com.android.proxy_self.presentation.viewmodel" id="-699381481%2FPackages%2F492899073" data-filterable-set=":app:dokkaHtml/release"></a>
    <div class="table-row" data-filterable-current=":app:dokkaHtml/release" data-filterable-set=":app:dokkaHtml/release">
      <div>
        <div class="main-subrow ">
          <div class=""><span class="inline-flex">
              <div><a href="-proxy -self -android -app/com.android.proxy_self.presentation.viewmodel/index.html">com.android.proxy_self.presentation.viewmodel</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-699381481%2FPackages%2F492899073"></span>
                <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
              </span></span></div>
          <div class="pull-right">
            <div class="platform-tags no-gutters">
              <div class="platform-tag jvm-like">androidJvm</div>
            </div>
          </div>
        </div>
        <div></div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>