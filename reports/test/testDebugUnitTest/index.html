<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">15</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.509s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.android.proxy_self.html">com.android.proxy_self</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.437s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.android.proxy_self.data.mappers.html">com.android.proxy_self.data.mappers</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.068s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.android.proxy_self.domain.entities.html">com.android.proxy_self.domain.entities</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.android.proxy_self.ExampleUnitTest.html">com.android.proxy_self.ExampleUnitTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.437s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.android.proxy_self.data.mappers.ProxyMapperTest.html">com.android.proxy_self.data.mappers.ProxyMapperTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.068s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.android.proxy_self.domain.entities.ProxyConfigTest.html">com.android.proxy_self.domain.entities.ProxyConfigTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.13</a> at Jul 13, 2025, 5:45:48 PM</p>
</div>
</div>
</body>
</html>
