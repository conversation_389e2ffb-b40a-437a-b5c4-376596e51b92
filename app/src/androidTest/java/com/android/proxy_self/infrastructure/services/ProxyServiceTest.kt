package com.android.proxy_self.infrastructure.services

import android.content.Context
import android.content.Intent
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.rule.ServiceTestRule
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyType
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import java.util.concurrent.TimeoutException
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@RunWith(AndroidJUnit4::class)
class ProxyServiceTest {

    @get:Rule
    val serviceRule = ServiceTestRule()

    private lateinit var context: Context

    private val testConfig = ProxyConfig(
        id = 1,
        name = "Test Proxy",
        serverAddress = "127.0.0.1",
        serverPort = 1080,
        username = "testuser",
        password = "testpass",
        proxyType = ProxyType.SOCKS5,
        domains = listOf("example.com"),
        isEnabled = true
    )

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        // Clean up any running services
        try {
            val stopIntent = Intent(context, ProxyService::class.java).apply {
                action = ProxyService.ACTION_STOP_PROXY
            }
            context.stopService(stopIntent)
        } catch (e: Exception) {
            // Ignore cleanup errors
        }
    }

    @Test
    fun testServiceCanBeStarted() {
        // Given
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, testConfig)
        }

        // When
        val binder = serviceRule.bindService(serviceIntent)

        // Then
        assertNotNull(binder)
    }

    @Test
    fun testServiceCanBeStopped() {
        // Given - start the service first
        val startIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, testConfig)
        }
        serviceRule.bindService(startIntent)

        // When
        val stopIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_STOP_PROXY
        }
        val result = context.stopService(stopIntent)

        // Then
        assertTrue(result)
    }

    @Test
    fun testServiceHandlesTestConnectionAction() {
        // Given
        val testIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_TEST_CONNECTION
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, testConfig)
        }

        // When
        val binder = serviceRule.bindService(testIntent)

        // Then
        assertNotNull(binder)
    }

    @Test
    fun testServiceHandlesInvalidAction() {
        // Given
        val invalidIntent = Intent(context, ProxyService::class.java).apply {
            action = "INVALID_ACTION"
        }

        // When & Then - should not throw exception
        try {
            serviceRule.bindService(invalidIntent)
        } catch (e: TimeoutException) {
            // Expected for invalid actions
        }
    }

    @Test
    fun testServiceHandlesMissingConfig() {
        // Given
        val intentWithoutConfig = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            // No config provided
        }

        // When & Then - should not crash
        try {
            serviceRule.bindService(intentWithoutConfig)
        } catch (e: TimeoutException) {
            // Expected when config is missing
        }
    }

    @Test
    fun testServiceWithDifferentProxyTypes() {
        // Given
        val httpConfig = testConfig.copy(proxyType = ProxyType.HTTP)
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, httpConfig)
        }

        // When
        val binder = serviceRule.bindService(serviceIntent)

        // Then
        assertNotNull(binder)
    }

    @Test
    fun testServiceWithEmptyDomains() {
        // Given
        val configWithEmptyDomains = testConfig.copy(domains = emptyList())
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, configWithEmptyDomains)
        }

        // When
        val binder = serviceRule.bindService(serviceIntent)

        // Then
        assertNotNull(binder)
    }

    @Test
    fun testServiceWithoutCredentials() {
        // Given
        val configWithoutCreds = testConfig.copy(username = "", password = "")
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, configWithoutCreds)
        }

        // When
        val binder = serviceRule.bindService(serviceIntent)

        // Then
        assertNotNull(binder)
    }

    @Test
    fun testServiceRestartability() {
        // Given
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, testConfig)
        }

        // When - start, stop, and start again
        val binder1 = serviceRule.bindService(serviceIntent)
        assertNotNull(binder1)
        
        serviceRule.unbindService()
        
        val binder2 = serviceRule.bindService(serviceIntent)

        // Then
        assertNotNull(binder2)
    }

    @Test
    fun testServiceWithInvalidPort() {
        // Given
        val configWithInvalidPort = testConfig.copy(serverPort = -1)
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, configWithInvalidPort)
        }

        // When & Then - should handle gracefully
        try {
            serviceRule.bindService(serviceIntent)
        } catch (e: TimeoutException) {
            // Expected for invalid configuration
        }
    }

    @Test
    fun testServiceWithEmptyServerAddress() {
        // Given
        val configWithEmptyServer = testConfig.copy(serverAddress = "")
        val serviceIntent = Intent(context, ProxyService::class.java).apply {
            action = ProxyService.ACTION_START_PROXY
            putExtra(ProxyService.EXTRA_PROXY_CONFIG, configWithEmptyServer)
        }

        // When & Then - should handle gracefully
        try {
            serviceRule.bindService(serviceIntent)
        } catch (e: TimeoutException) {
            // Expected for invalid configuration
        }
    }
}
