package com.android.proxy_self.data.local

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.android.proxy_self.data.local.dao.ProxyConfigDao
import com.android.proxy_self.data.local.database.ProxyDatabase
import com.android.proxy_self.data.local.entities.ProxyConfigEntity
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@RunWith(AndroidJUnit4::class)
class ProxyDatabaseTest {

    private lateinit var database: ProxyDatabase
    private lateinit var proxyConfigDao: ProxyConfigDao

    private val testEntity = ProxyConfigEntity(
        id = 1,
        name = "Test Proxy",
        serverAddress = "127.0.0.1",
        serverPort = 1080,
        username = "testuser",
        password = "testpass",
        proxyType = "SOCKS5",
        domains = "example.com,google.com",
        isEnabled = true,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )

    @Before
    fun setUp() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        database = Room.inMemoryDatabaseBuilder(
            context,
            ProxyDatabase::class.java
        ).build()
        proxyConfigDao = database.proxyConfigDao()
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun testInsertAndGetProxyConfig() = runTest {
        // When
        proxyConfigDao.insert(testEntity)
        val result = proxyConfigDao.getById(1)

        // Then
        assertNotNull(result)
        assertEquals(testEntity.name, result.name)
        assertEquals(testEntity.serverAddress, result.serverAddress)
        assertEquals(testEntity.serverPort, result.serverPort)
        assertEquals(testEntity.username, result.username)
        assertEquals(testEntity.password, result.password)
        assertEquals(testEntity.proxyType, result.proxyType)
        assertEquals(testEntity.domains, result.domains)
        assertEquals(testEntity.isEnabled, result.isEnabled)
    }

    @Test
    fun testGetAllProxyConfigs() = runTest {
        // Given
        val entity1 = testEntity.copy(id = 1, name = "Proxy 1")
        val entity2 = testEntity.copy(id = 2, name = "Proxy 2")

        // When
        proxyConfigDao.insert(entity1)
        proxyConfigDao.insert(entity2)
        val result = proxyConfigDao.getAll().first()

        // Then
        assertEquals(2, result.size)
        assertTrue(result.any { it.name == "Proxy 1" })
        assertTrue(result.any { it.name == "Proxy 2" })
    }

    @Test
    fun testUpdateProxyConfig() = runTest {
        // Given
        proxyConfigDao.insert(testEntity)

        // When
        val updatedEntity = testEntity.copy(
            name = "Updated Proxy",
            serverAddress = "***********",
            updatedAt = System.currentTimeMillis()
        )
        proxyConfigDao.update(updatedEntity)
        val result = proxyConfigDao.getById(1)

        // Then
        assertNotNull(result)
        assertEquals("Updated Proxy", result.name)
        assertEquals("***********", result.serverAddress)
    }

    @Test
    fun testDeleteProxyConfig() = runTest {
        // Given
        proxyConfigDao.insert(testEntity)

        // When
        proxyConfigDao.delete(testEntity)
        val result = proxyConfigDao.getById(1)

        // Then
        assertEquals(null, result)
    }

    @Test
    fun testDeleteById() = runTest {
        // Given
        proxyConfigDao.insert(testEntity)

        // When
        proxyConfigDao.deleteById(1)
        val result = proxyConfigDao.getById(1)

        // Then
        assertEquals(null, result)
    }

    @Test
    fun testGetEnabledConfigs() = runTest {
        // Given
        val enabledEntity = testEntity.copy(id = 1, name = "Enabled", isEnabled = true)
        val disabledEntity = testEntity.copy(id = 2, name = "Disabled", isEnabled = false)

        // When
        proxyConfigDao.insert(enabledEntity)
        proxyConfigDao.insert(disabledEntity)
        val result = proxyConfigDao.getEnabledConfigs().first()

        // Then
        assertEquals(1, result.size)
        assertEquals("Enabled", result[0].name)
        assertTrue(result[0].isEnabled)
    }

    @Test
    fun testEnableConfig() = runTest {
        // Given
        val disabledEntity = testEntity.copy(isEnabled = false)
        proxyConfigDao.insert(disabledEntity)

        // When
        proxyConfigDao.enableConfig(1)
        val result = proxyConfigDao.getById(1)

        // Then
        assertNotNull(result)
        assertTrue(result.isEnabled)
    }

    @Test
    fun testDisableConfig() = runTest {
        // Given
        val enabledEntity = testEntity.copy(isEnabled = true)
        proxyConfigDao.insert(enabledEntity)

        // When
        proxyConfigDao.disableConfig(1)
        val result = proxyConfigDao.getById(1)

        // Then
        assertNotNull(result)
        assertTrue(!result.isEnabled)
    }

    @Test
    fun testDisableAllConfigs() = runTest {
        // Given
        val entity1 = testEntity.copy(id = 1, name = "Proxy 1", isEnabled = true)
        val entity2 = testEntity.copy(id = 2, name = "Proxy 2", isEnabled = true)

        // When
        proxyConfigDao.insert(entity1)
        proxyConfigDao.insert(entity2)
        proxyConfigDao.disableAllConfigs()
        val result = proxyConfigDao.getAll().first()

        // Then
        assertEquals(2, result.size)
        assertTrue(result.all { !it.isEnabled })
    }

    @Test
    fun testGetConfigsByType() = runTest {
        // Given
        val socks5Entity = testEntity.copy(id = 1, name = "SOCKS5", proxyType = "SOCKS5")
        val httpEntity = testEntity.copy(id = 2, name = "HTTP", proxyType = "HTTP")

        // When
        proxyConfigDao.insert(socks5Entity)
        proxyConfigDao.insert(httpEntity)
        val result = proxyConfigDao.getConfigsByType("SOCKS5").first()

        // Then
        assertEquals(1, result.size)
        assertEquals("SOCKS5", result[0].name)
        assertEquals("SOCKS5", result[0].proxyType)
    }

    @Test
    fun testSearchConfigs() = runTest {
        // Given
        val entity1 = testEntity.copy(id = 1, name = "Test Proxy 1")
        val entity2 = testEntity.copy(id = 2, name = "Production Proxy")
        val entity3 = testEntity.copy(id = 3, name = "Test Proxy 2")

        // When
        proxyConfigDao.insert(entity1)
        proxyConfigDao.insert(entity2)
        proxyConfigDao.insert(entity3)
        val result = proxyConfigDao.searchConfigs("Test").first()

        // Then
        assertEquals(2, result.size)
        assertTrue(result.all { it.name.contains("Test") })
    }

    @Test
    fun testGetConfigCount() = runTest {
        // Given
        val entity1 = testEntity.copy(id = 1)
        val entity2 = testEntity.copy(id = 2)

        // When
        proxyConfigDao.insert(entity1)
        proxyConfigDao.insert(entity2)
        val result = proxyConfigDao.getConfigCount().first()

        // Then
        assertEquals(2, result)
    }

    @Test
    fun testDatabaseConstraints() = runTest {
        // Given - try to insert duplicate IDs
        val entity1 = testEntity.copy(id = 1, name = "First")
        val entity2 = testEntity.copy(id = 1, name = "Second")

        // When
        proxyConfigDao.insert(entity1)
        
        try {
            proxyConfigDao.insert(entity2)
        } catch (e: Exception) {
            // Expected - primary key constraint
        }

        val result = proxyConfigDao.getById(1)

        // Then
        assertNotNull(result)
        assertEquals("First", result.name) // Original should remain
    }

    @Test
    fun testEmptyDatabase() = runTest {
        // When
        val allConfigs = proxyConfigDao.getAll().first()
        val enabledConfigs = proxyConfigDao.getEnabledConfigs().first()
        val count = proxyConfigDao.getConfigCount().first()

        // Then
        assertTrue(allConfigs.isEmpty())
        assertTrue(enabledConfigs.isEmpty())
        assertEquals(0, count)
    }
}
