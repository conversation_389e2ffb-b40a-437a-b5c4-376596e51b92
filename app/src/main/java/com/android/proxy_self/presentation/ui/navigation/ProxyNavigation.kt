package com.android.proxy_self.presentation.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.android.proxy_self.presentation.ui.screens.AboutScreen
import com.android.proxy_self.presentation.ui.screens.ProxyConfigScreen
import com.android.proxy_self.presentation.ui.screens.SettingsScreen

/**
 * Navigation destinations for the app
 */
sealed class Screen(val route: String) {
    object ProxyConfig : Screen("proxy_config")

    object Settings : Screen("settings")

    object About : Screen("about")
}

/**
 * Main navigation composable for the app
 * Implements Jetpack Compose Navigation
 */
@Composable
fun ProxyNavigation(
    navController: NavHostController = rememberNavController(),
    onRequestVpnPermission: () -> Unit = {}
) {
    NavHost(
        navController = navController,
        startDestination = Screen.ProxyConfig.route,
    ) {
        composable(Screen.ProxyConfig.route) {
            ProxyConfigScreen(
                onNavigateToSettings = {
                    navController.navigate(Screen.Settings.route)
                },
                onNavigateToAbout = {
                    navController.navigate(Screen.About.route)
                },
                onRequestVpnPermission = onRequestVpnPermission
            )
        }

        composable(Screen.Settings.route) {
            SettingsScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToAbout = {
                    navController.navigate(Screen.About.route)
                },
            )
        }

        composable(Screen.About.route) {
            AboutScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
            )
        }
    }
}
