package com.android.proxy_self.presentation.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.style.TextAlign

/**
 * Dialog to explain VPN permission requirement
 */
@Composable
fun VpnPermissionDialog(
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                imageVector = Icons.Default.Security,
                contentDescription = "VPN Permission"
            )
        },
        title = {
            Text(
                text = "VPN Permission Required",
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = "This app needs VPN permission to intercept network traffic and route specific domains through your proxy server.\n\n" +
                        "Your data will only be routed through the proxy for domains you specify. " +
                        "No data is collected or monitored by this app.",
                textAlign = TextAlign.Start
            )
        },
        confirmButton = {
            Button(onClick = onConfirm) {
                Text("Grant Permission")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
