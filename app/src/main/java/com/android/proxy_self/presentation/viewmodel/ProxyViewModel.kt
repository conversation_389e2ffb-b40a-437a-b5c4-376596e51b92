package com.android.proxy_self.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyType
import com.android.proxy_self.domain.usecases.*
import com.android.proxy_self.infrastructure.utils.DebugLogger
import com.android.proxy_self.infrastructure.utils.ValidationUtils
import com.android.proxy_self.presentation.state.ConnectionTestResult
import com.android.proxy_self.presentation.state.ProxyUiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for proxy configuration screen
 * Implements MVVM pattern with proper state management
 * Follows Single Responsibility Principle
 */
@HiltViewModel
class ProxyViewModel
    @Inject
    constructor(
        private val startProxyUseCase: StartProxyUseCase,
        private val stopProxyUseCase: StopProxyUseCase,
        private val saveProxyConfigUseCase: SaveProxyConfigUseCase,
        private val getProxyConfigUseCase: GetProxyConfigUseCase,
        private val testProxyConnectionUseCase: TestProxyConnectionUseCase,
        private val validationUtils: ValidationUtils,
    ) : ViewModel() {
        private val _uiState = MutableStateFlow(ProxyUiState())
        val uiState: StateFlow<ProxyUiState> = _uiState.asStateFlow()

        init {
            DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "ProxyViewModel initialized")
            loadProxyConfig()
        }

        /**
         * Load saved proxy configuration
         */
        private fun loadProxyConfig() {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isLoading = true)

                getProxyConfigUseCase().collect { result ->
                    result.fold(
                        onSuccess = { config ->
                            updateUiStateWithConfig(config)
                        },
                        onFailure = { error ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isLoading = false,
                                    error = error.message,
                                )
                        },
                    )
                }
            }
        }

        /**
         * Update UI state with loaded configuration
         */
        private fun updateUiStateWithConfig(config: ProxyConfig) {
            _uiState.value =
                _uiState.value.copy(
                    proxyConfig = config,
                    serverAddress = config.serverAddress,
                    serverPort = config.serverPort.toString().takeIf { it != "0" } ?: "",
                    username = config.username,
                    password = config.password,
                    domains = config.getDomainsAsString(),
                    selectedProxyType = config.proxyType,
                    isProxyEnabled = config.isEnabled,
                    isLoading = false,
                )
        }

        /**
         * Update server address
         */
        fun updateServerAddress(address: String) {
            _uiState.value =
                _uiState.value.copy(
                    serverAddress = address,
                    validationErrors = _uiState.value.validationErrors - "serverAddress",
                )
            validateServerAddress(address)
        }

        /**
         * Update server port
         */
        fun updateServerPort(port: String) {
            _uiState.value =
                _uiState.value.copy(
                    serverPort = port,
                    validationErrors = _uiState.value.validationErrors - "serverPort",
                )
            validateServerPort(port)
        }

        /**
         * Update username
         */
        fun updateUsername(username: String) {
            _uiState.value =
                _uiState.value.copy(
                    username = username,
                    validationErrors = _uiState.value.validationErrors - "username",
                )
            validateUsername(username)
        }

        /**
         * Update password
         */
        fun updatePassword(password: String) {
            _uiState.value =
                _uiState.value.copy(
                    password = password,
                    validationErrors = _uiState.value.validationErrors - "password",
                )
            validatePassword(password)
        }

        /**
         * Update domains
         */
        fun updateDomains(domains: String) {
            _uiState.value =
                _uiState.value.copy(
                    domains = domains,
                    validationErrors = _uiState.value.validationErrors - "domains",
                )
            validateDomains(domains)
        }

        /**
         * Update proxy type
         */
        fun updateProxyType(proxyType: ProxyType) {
            _uiState.value = _uiState.value.copy(selectedProxyType = proxyType)
        }

        /**
         * Toggle password visibility
         */
        fun togglePasswordVisibility() {
            _uiState.value =
                _uiState.value.copy(
                    isPasswordVisible = !_uiState.value.isPasswordVisible,
                )
        }

        /**
         * Start proxy service
         */
        fun startProxy() {
            DebugLogger.methodEntry(DebugLogger.Tags.VIEWMODEL, "startProxy")

            if (!_uiState.value.isFormValid()) {
                DebugLogger.w(DebugLogger.Tags.VIEWMODEL, "Form validation failed, cannot start proxy")
                validateAllFields()
                return
            }

            viewModelScope.launch {
                val config = _uiState.value.toProxyConfig()
                DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Starting proxy with config: ${config.serverAddress}:${config.serverPort}")

                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                startProxyUseCase(config).collect { result ->
                    result.fold(
                        onSuccess = { success ->
                            DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "Proxy start result: $success")
                            _uiState.value =
                                _uiState.value.copy(
                                    isLoading = false,
                                    isProxyEnabled = success,
                                    successMessage = if (success) "Proxy started successfully" else null,
                                )
                        },
                        onFailure = { error ->
                            DebugLogger.e(DebugLogger.Tags.VIEWMODEL, "Failed to start proxy", error)
                            _uiState.value =
                                _uiState.value.copy(
                                    isLoading = false,
                                    error = error.message,
                                )
                        },
                    )
                }
            }

            DebugLogger.methodExit(DebugLogger.Tags.VIEWMODEL, "startProxy")
        }

        /**
         * Stop proxy service
         */
        fun stopProxy() {
            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                stopProxyUseCase().collect { result ->
                    result.fold(
                        onSuccess = { success ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isLoading = false,
                                    isProxyEnabled = !success,
                                    successMessage = if (success) "Proxy stopped successfully" else null,
                                )
                        },
                        onFailure = { error ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isLoading = false,
                                    error = error.message,
                                )
                        },
                    )
                }
            }
        }

        /**
         * Save proxy configuration
         */
        fun saveConfiguration() {
            if (!_uiState.value.isFormValid()) {
                validateAllFields()
                return
            }

            viewModelScope.launch {
                _uiState.value = _uiState.value.copy(isSaving = true, error = null)

                val config = _uiState.value.toProxyConfig()

                saveProxyConfigUseCase(config).collect { result ->
                    result.fold(
                        onSuccess = { success ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isSaving = false,
                                    successMessage = if (success) "Configuration saved successfully" else null,
                                )
                        },
                        onFailure = { error ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isSaving = false,
                                    error = error.message,
                                )
                        },
                    )
                }
            }
        }

        /**
         * Test proxy connection
         */
        fun testConnection() {
            if (!_uiState.value.isFormValid()) {
                validateAllFields()
                return
            }

            viewModelScope.launch {
                _uiState.value =
                    _uiState.value.copy(
                        isTestingConnection = true,
                        connectionTestResult = null,
                        error = null,
                    )

                val config = _uiState.value.toProxyConfig()

                testProxyConnectionUseCase(config).collect { result ->
                    result.fold(
                        onSuccess = { success ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isTestingConnection = false,
                                    connectionTestResult =
                                        if (success) {
                                            ConnectionTestResult.Success
                                        } else {
                                            ConnectionTestResult.Failure("Connection failed")
                                        },
                                )
                        },
                        onFailure = { error ->
                            _uiState.value =
                                _uiState.value.copy(
                                    isTestingConnection = false,
                                    connectionTestResult =
                                        ConnectionTestResult.Failure(
                                            error.message ?: "Unknown error",
                                        ),
                                )
                        },
                    )
                }
            }
        }

        /**
         * Clear error message
         */
        fun clearError() {
            _uiState.value = _uiState.value.copy(error = null)
        }

        /**
         * Clear success message
         */
        fun clearSuccessMessage() {
            _uiState.value = _uiState.value.copy(successMessage = null)
        }

        /**
         * Clear connection test result
         */
        fun clearConnectionTestResult() {
            _uiState.value = _uiState.value.copy(connectionTestResult = null)
        }

        /**
         * Toggle password visibility
         */
        fun togglePasswordVisibility() {
            _uiState.value = _uiState.value.copy(isPasswordVisible = !_uiState.value.isPasswordVisible)
        }

        /**
         * Handle VPN permission granted
         */
        fun onVpnPermissionGranted() {
            DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "VPN permission granted, starting proxy")
            startProxy()
        }

        /**
         * Handle VPN permission denied
         */
        fun onVpnPermissionDenied() {
            DebugLogger.d(DebugLogger.Tags.VIEWMODEL, "VPN permission denied")
            _uiState.value = _uiState.value.copy(
                error = "VPN permission is required to use proxy functionality"
            )
        }

        // Validation methods
        private fun validateServerAddress(address: String) {
            val error = validationUtils.getIpValidationError(address)
            updateValidationError("serverAddress", error)
        }

        private fun validateServerPort(port: String) {
            val error = validationUtils.getPortValidationError(port)
            updateValidationError("serverPort", error)
        }

        private fun validateUsername(username: String) {
            val error = validationUtils.getUsernameValidationError(username)
            updateValidationError("username", error)
        }

        private fun validatePassword(password: String) {
            val error = validationUtils.getPasswordValidationError(password)
            updateValidationError("password", error)
        }

        private fun validateDomains(domains: String) {
            val error = validationUtils.getDomainsValidationError(domains)
            updateValidationError("domains", error)
        }

        private fun validateAllFields() {
            val currentState = _uiState.value
            validateServerAddress(currentState.serverAddress)
            validateServerPort(currentState.serverPort)
            validateUsername(currentState.username)
            validatePassword(currentState.password)
            validateDomains(currentState.domains)
        }

        private fun updateValidationError(
            field: String,
            error: String?,
        ) {
            val currentErrors = _uiState.value.validationErrors.toMutableMap()
            if (error != null) {
                currentErrors[field] = error
            } else {
                currentErrors.remove(field)
            }
            _uiState.value = _uiState.value.copy(validationErrors = currentErrors)
        }
    }
