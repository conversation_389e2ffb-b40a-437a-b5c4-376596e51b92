package com.android.proxy_self.infrastructure.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.net.VpnService
import android.os.Build
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import com.android.proxy_self.MainActivity
import com.android.proxy_self.R
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.infrastructure.managers.NetworkManager
import com.android.proxy_self.infrastructure.managers.ProxyManager
import com.android.proxy_self.infrastructure.utils.DebugLogger
import com.android.proxy_self.infrastructure.utils.DomainExtractor
import com.android.proxy_self.infrastructure.utils.Socks5Client
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import java.io.FileInputStream
import java.io.FileOutputStream
import java.net.InetAddress
import java.net.Socket
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

/**
 * VPN Service for intercepting network traffic and routing through proxy
 * Extends Android VpnService to create a VPN tunnel
 * Implements domain-based routing logic
 */
@AndroidEntryPoint
class ProxyVpnService : VpnService() {
    @Inject
    lateinit var proxyManager: ProxyManager

    @Inject
    lateinit var networkManager: NetworkManager

    private var vpnInterface: ParcelFileDescriptor? = null
    private var serviceJob: Job? = null
    private var isRunning = false
    private var currentConfig: ProxyConfig? = null

    // SOCKS5 connection pool for reusing connections
    private val connectionPool = ConcurrentHashMap<String, Socket>()
    private var socks5Client: Socks5Client? = null

    // Circuit breaker for SOCKS5 connections
    private var failedConnectionCount = 0
    private var lastFailureTime = 0L
    private val maxFailures = 5
    private val circuitBreakerTimeout = 30000L // 30 seconds

    // Cache DNS resolutions: IP -> Domain mapping
    private val dnsCache = mutableMapOf<String, String>()

    // Cache domain -> IPs mapping for automatic resolution
    private val domainToIpCache = mutableMapOf<String, MutableSet<String>>()

    companion object {
        const val ACTION_START_VPN = "com.android.proxy_self.START_VPN"
        const val ACTION_STOP_VPN = "com.android.proxy_self.STOP_VPN"
        const val EXTRA_PROXY_CONFIG = "proxy_config"

        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "proxy_vpn_channel"
        private const val VPN_MTU = 1500
        private const val VPN_ADDRESS = "********"
        private const val VPN_ROUTE = "0.0.0.0"

        // DEBUG MODE: Set to true to route all traffic through proxy for testing
        private const val DEBUG_ROUTE_ALL_TRAFFIC = false
    }

    override fun onCreate() {
        super.onCreate()
        DebugLogger.d(DebugLogger.Tags.VPN, "ProxyVpnService created")
        createNotificationChannel()
    }

    override fun onStartCommand(
        intent: Intent?,
        flags: Int,
        startId: Int,
    ): Int {
        android.util.Log.d("ProxyVpnService", "onStartCommand called with action: ${intent?.action}")

        when (intent?.action) {
            ACTION_START_VPN -> {
                val config =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG, ProxyConfig::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG)
                    }

                if (config != null) {
                    android.util.Log.d("ProxyVpnService", "Starting VPN with config: ${config.serverAddress}:${config.serverPort}")
                    startVpn(config)
                } else {
                    android.util.Log.e("ProxyVpnService", "No proxy config provided, stopping service")
                    stopSelf()
                }
            }
            ACTION_STOP_VPN -> {
                android.util.Log.d("ProxyVpnService", "Stopping VPN service")
                stopVpn()
            }
            else -> {
                android.util.Log.w("ProxyVpnService", "Unknown action or no action provided: ${intent?.action}")
                stopSelf()
            }
        }
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        stopVpn()
    }

    /**
     * Start VPN with given proxy configuration
     */
    private fun startVpn(config: ProxyConfig) {
        if (isRunning) {
            android.util.Log.d("ProxyVpnService", "VPN already running, ignoring start request")
            return
        }

        // Check VPN permission
        val vpnIntent = VpnService.prepare(this)
        if (vpnIntent != null) {
            DebugLogger.e(DebugLogger.Tags.VPN, "VPN permission not granted")
            android.util.Log.e("ProxyVpnService", "VPN permission not granted - stopping service")
            stopSelf()
            return
        }

        android.util.Log.i("ProxyVpnService", "Starting VPN with config: ${config.serverAddress}:${config.serverPort}")

        try {
            currentConfig = config

            // Initialize SOCKS5 client
            socks5Client = Socks5Client(
                proxyHost = config.serverAddress,
                proxyPort = config.serverPort,
                username = config.username,
                password = config.password
            )

            // Create VPN interface
            vpnInterface = createVpnInterface()

            if (vpnInterface != null) {
                isRunning = true
                startForeground(NOTIFICATION_ID, createNotification(config))

                android.util.Log.i("ProxyVpnService", "VPN started successfully with proxy ${config.serverAddress}:${config.serverPort}")

                // Start packet processing in background
                serviceJob =
                    CoroutineScope(Dispatchers.IO).launch {
                        processPackets()
                    }
            } else {
                android.util.Log.e("ProxyVpnService", "Failed to create VPN interface - stopping service")
                stopSelf()
                return
            }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Failed to start VPN", e)
            stopVpn()
        }
    }

    /**
     * Stop VPN service
     */
    private fun stopVpn() {
        android.util.Log.d("ProxyVpnService", "Stopping VPN service")
        isRunning = false
        serviceJob?.cancel()

        // Close all SOCKS5 connections
        connectionPool.values.forEach { socket ->
            try {
                socket.close()
            } catch (e: Exception) {
                android.util.Log.e("ProxyVpnService", "Error closing socket", e)
            }
        }
        connectionPool.clear()

        vpnInterface?.close()
        vpnInterface = null
        currentConfig = null
        socks5Client = null
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            stopForeground(STOP_FOREGROUND_REMOVE)
        } else {
            @Suppress("DEPRECATION")
            stopForeground(true)
        }
        stopSelf()
    }

    /**
     * Create VPN interface
     */
    private fun createVpnInterface(): ParcelFileDescriptor? {
        android.util.Log.d("ProxyVpnService", "Creating VPN interface...")

        try {
            val vpnInterface = Builder()
                .setMtu(VPN_MTU)
                .addAddress(VPN_ADDRESS, 32)
                .addRoute(VPN_ROUTE, 0)
                .addDnsServer("*******")
                .addDnsServer("*******")
                .setSession("ProxyVPN")
                .setConfigureIntent(
                    PendingIntent.getActivity(
                        this,
                        0,
                        Intent(this, MainActivity::class.java),
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                    ),
                )
                .establish()

            if (vpnInterface != null) {
                android.util.Log.i("ProxyVpnService", "VPN interface created successfully")
            } else {
                android.util.Log.e("ProxyVpnService", "Failed to create VPN interface - establish() returned null")
            }

            return vpnInterface
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Exception creating VPN interface", e)
            return null
        }
    }

    /**
     * Process network packets
     */
    private suspend fun processPackets() {
        val vpnInput = FileInputStream(vpnInterface?.fileDescriptor)
        val vpnOutput = FileOutputStream(vpnInterface?.fileDescriptor)

        val packet = ByteArray(32767)

        try {
            while (isRunning && !serviceJob?.isCancelled!!) {
                val length = vpnInput.read(packet)
                if (length > 0) {
                    // Process the packet
                    processPacket(ByteBuffer.wrap(packet, 0, length), vpnOutput)
                }
            }
        } catch (e: Exception) {
            // Handle packet processing errors
        } finally {
            vpnInput.close()
            vpnOutput.close()
        }
    }

    /**
     * Process individual network packet
     */
    private suspend fun processPacket(
        packet: ByteBuffer,
        vpnOutput: FileOutputStream,
    ) {
        try {
            android.util.Log.v("ProxyVpnService", "Processing packet of size: ${packet.remaining()}")

            // Parse IP packet
            val ipPacket = parseIpPacket(packet)

            if (ipPacket != null) {
                android.util.Log.v("ProxyVpnService", "Parsed IP packet: protocol=${ipPacket.protocol}")

                val domain = extractDomainFromPacket(ipPacket)
                android.util.Log.v("ProxyVpnService", "Extracted domain: $domain")

                // Extract destination IP
                val destIp = ipPacket.destIp
                val destAddress = "${destIp[0].toInt() and 0xFF}.${destIp[1].toInt() and 0xFF}.${destIp[2].toInt() and 0xFF}.${destIp[3].toInt() and 0xFF}"
                android.util.Log.v("ProxyVpnService", "Destination IP: $destAddress")

                // Handle DNS packets for automatic domain resolution
                if (ipPacket.protocol == 17) { // UDP
                    handleDnsPacket(ipPacket, domain, destAddress)
                }

                // Check if destination IP is cached from previous DNS resolution
                val cachedDomain = dnsCache[destAddress]
                if (cachedDomain != null) {
                    android.util.Log.d("ProxyVpnService", "Found cached domain '$cachedDomain' for IP $destAddress")
                }

                // DEBUG MODE: Route all traffic through proxy for testing
                if (DEBUG_ROUTE_ALL_TRAFFIC && currentConfig != null) {
                    android.util.Log.i("ProxyVpnService", "DEBUG MODE: Routing all traffic through proxy")
                    routePacketThroughProxy(ipPacket, vpnOutput)
                } else if (currentConfig != null) {
                    // Check domain, cached domain, and IP against whitelist
                    val shouldUseProxyForDomain = domain?.let { proxyManager.shouldUseProxy(it, currentConfig!!.domains) } ?: false
                    val shouldUseProxyForCachedDomain = cachedDomain?.let { proxyManager.shouldUseProxy(it, currentConfig!!.domains) } ?: false
                    val shouldUseProxyForIp = proxyManager.shouldUseProxy(destAddress, currentConfig!!.domains)
                    val shouldUseProxy = shouldUseProxyForDomain || shouldUseProxyForCachedDomain || shouldUseProxyForIp

                    android.util.Log.d("ProxyVpnService", "Domain '$domain' should use proxy: $shouldUseProxyForDomain")
                    android.util.Log.d("ProxyVpnService", "Cached domain '$cachedDomain' should use proxy: $shouldUseProxyForCachedDomain")
                    android.util.Log.d("ProxyVpnService", "IP '$destAddress' should use proxy: $shouldUseProxyForIp")
                    android.util.Log.d("ProxyVpnService", "Final decision - should use proxy: $shouldUseProxy")

                    if (shouldUseProxy) {
                        val target = domain ?: destAddress
                        android.util.Log.i("ProxyVpnService", "Routing '$target' through proxy")
                        // Route through proxy
                        routePacketThroughProxy(ipPacket, vpnOutput)
                    } else {
                        val target = domain ?: destAddress
                        android.util.Log.v("ProxyVpnService", "Routing '$target' directly (not in whitelist)")
                        // Route directly
                        routePacketDirectly(ipPacket, vpnOutput)
                    }
                } else {
                    // Default: route directly
                    if (domain == null) {
                        android.util.Log.v("ProxyVpnService", "No domain extracted from packet, routing directly")
                    } else {
                        android.util.Log.v("ProxyVpnService", "No current config, routing directly")
                    }
                    routePacketDirectly(packet, vpnOutput)
                }
            } else {
                android.util.Log.v("ProxyVpnService", "Failed to parse IP packet, routing directly")
                routePacketDirectly(packet, vpnOutput)
            }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Error processing packet", e)
            // Route directly as fallback
            try {
                routePacketDirectly(packet, vpnOutput)
            } catch (fallbackError: Exception) {
                android.util.Log.e("ProxyVpnService", "Fallback routing also failed", fallbackError)
            }
        }
    }

    /**
     * Parse IP packet (simplified implementation)
     */
    private fun parseIpPacket(packet: ByteBuffer): IpPacket? {
        return try {
            if (packet.remaining() < 20) return null

            val version = (packet.get(0).toInt() and 0xF0) shr 4
            if (version != 4) return null // Only IPv4 for now

            val protocol = packet.get(9).toInt() and 0xFF
            val sourceIp = ByteArray(4)
            val destIp = ByteArray(4)

            packet.position(12)
            packet.get(sourceIp)
            packet.get(destIp)

            IpPacket(
                version = version,
                protocol = protocol,
                sourceIp = sourceIp,
                destIp = destIp,
                data = packet.array(),
            )
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Extract domain from packet using DomainExtractor utility
     */
    private fun extractDomainFromPacket(packet: IpPacket): String? {
        return try {
            val buffer = ByteBuffer.wrap(packet.data)
            DomainExtractor.extractDomain(buffer)
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Error extracting domain", e)
            null
        }
    }

    /**
     * Handle DNS packets for automatic domain resolution
     */
    private fun handleDnsPacket(ipPacket: IpPacket, domain: String?, destAddress: String) {
        try {
            if (domain != null) {
                android.util.Log.d("ProxyVpnService", "DNS query detected for domain '$domain'")

                // Pre-resolve domains that should use proxy and cache the mappings
                if (currentConfig != null && proxyManager.shouldUseProxy(domain, currentConfig!!.domains)) {
                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            val resolvedIps = resolveDomainToIps(domain)
                            if (resolvedIps.isNotEmpty()) {
                                android.util.Log.i("ProxyVpnService", "Pre-resolved proxy domain '$domain' to IPs: ${resolvedIps.joinToString()}")

                                // Cache the mappings for proxy routing
                                resolvedIps.forEach { ip ->
                                    dnsCache[ip] = domain
                                    android.util.Log.i("ProxyVpnService", "Cached proxy mapping: $ip -> $domain")
                                }

                                // Also cache domain -> IPs mapping
                                domainToIpCache[domain] = resolvedIps.toMutableSet()
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("ProxyVpnService", "Failed to pre-resolve proxy domain '$domain'", e)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Error handling DNS packet", e)
        }
    }

    /**
     * Resolve domain to IP addresses
     */
    private suspend fun resolveDomainToIps(domain: String): List<String> = withContext(Dispatchers.IO) {
        try {
            val addresses = InetAddress.getAllByName(domain)
            addresses.map { it.hostAddress ?: "" }.filter { it.isNotEmpty() }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "DNS resolution failed for '$domain'", e)
            emptyList()
        }
    }

    /**
     * Route packet through SOCKS5 proxy
     */
    private suspend fun routePacketThroughProxy(
        packet: IpPacket,
        vpnOutput: FileOutputStream,
    ) {
        try {
            val client = socks5Client ?: return routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)

            // Check circuit breaker
            if (isCircuitBreakerOpen()) {
                android.util.Log.d("ProxyVpnService", "Circuit breaker open, routing directly")
                return routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
            }

            // Extract destination from packet
            val destIp = packet.destIp
            val destAddress = "${destIp[0].toInt() and 0xFF}.${destIp[1].toInt() and 0xFF}.${destIp[2].toInt() and 0xFF}.${destIp[3].toInt() and 0xFF}"

            // SOCKS5 only supports TCP, but we need to handle DNS responses for caching
            if (packet.protocol == 17) { // UDP
                android.util.Log.d("ProxyVpnService", "UDP packet detected")

                // Check if this is a DNS response that we need to intercept
                if (shouldInterceptDnsResponse(packet)) {
                    interceptDnsResponse(packet)
                }

                android.util.Log.d("ProxyVpnService", "Routing UDP directly (SOCKS5 doesn't support UDP)")
                return routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
            }

            // For TCP packets, extract port from TCP header
            if (packet.protocol == 6) { // TCP
                val tcpData = ByteBuffer.wrap(packet.data, 20, packet.data.size - 20) // Skip IP header
                if (tcpData.remaining() >= 4) {
                    tcpData.short // Skip source port
                    val destPort = tcpData.short.toInt() and 0xFFFF

                    val connectionKey = "$destAddress:$destPort"

                    // Try to get existing connection or create new one
                    val proxySocket = connectionPool[connectionKey] ?: run {
                        android.util.Log.d("ProxyVpnService", "Creating SOCKS5 connection to $destAddress:$destPort")
                        val socket = client.connect(destAddress, destPort)
                        if (socket != null) {
                            connectionPool[connectionKey] = socket
                            recordConnectionSuccess()
                            socket
                        } else {
                            android.util.Log.e("ProxyVpnService", "Failed to create SOCKS5 connection")
                            recordConnectionFailure()
                            return routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
                        }
                    }

                    // Forward packet data through proxy
                    try {
                        val tcpPayload = ByteArray(packet.data.size - 40) // Skip IP + TCP headers
                        System.arraycopy(packet.data, 40, tcpPayload, 0, tcpPayload.size)

                        if (tcpPayload.isNotEmpty()) {
                            proxySocket.getOutputStream().write(tcpPayload)
                            proxySocket.getOutputStream().flush()

                            // Read response (simplified - in real implementation would need proper handling)
                            val response = ByteArray(1024)
                            val bytesRead = proxySocket.getInputStream().read(response)

                            if (bytesRead > 0) {
                                // Create response packet and write back to VPN
                                val responsePacket = createResponsePacket(packet, response, bytesRead)
                                vpnOutput.write(responsePacket)
                                vpnOutput.flush()
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("ProxyVpnService", "Error forwarding through proxy", e)
                        connectionPool.remove(connectionKey)
                        proxySocket.close()
                        routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
                    }
                } else {
                    routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
                }
            } else {
                // For non-TCP packets, route directly for now
                routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
            }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Error routing packet through proxy", e)
            recordConnectionFailure()
            routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
        }
    }

    /**
     * Check if circuit breaker is open
     */
    private fun isCircuitBreakerOpen(): Boolean {
        val currentTime = System.currentTimeMillis()

        // Reset circuit breaker after timeout
        if (currentTime - lastFailureTime > circuitBreakerTimeout) {
            failedConnectionCount = 0
            return false
        }

        return failedConnectionCount >= maxFailures
    }

    /**
     * Check if we should intercept DNS response for caching
     */
    private fun shouldInterceptDnsResponse(packet: IpPacket): Boolean {
        try {
            // Check if this is coming from a DNS server (port 53)
            val srcIp = packet.sourceIp
            val srcAddress = "${(srcIp[0].toInt() and 0xFF)}.${(srcIp[1].toInt() and 0xFF)}.${(srcIp[2].toInt() and 0xFF)}.${(srcIp[3].toInt() and 0xFF)}"

            // Check if source port is 53 (DNS)
            if (packet.data.size >= 28) { // IP header (20) + UDP header (8)
                val udpSrcPort = ((packet.data[20].toInt() and 0xFF) shl 8) or (packet.data[21].toInt() and 0xFF)
                return udpSrcPort == 53
            }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Error checking DNS response", e)
        }
        return false
    }

    /**
     * Intercept DNS response to cache IP -> domain mappings
     */
    private fun interceptDnsResponse(packet: IpPacket) {
        try {
            android.util.Log.d("ProxyVpnService", "Intercepting DNS response for caching")

            // This is a simplified DNS response parser
            // In a production app, you'd want a more robust DNS parser
            val dnsData = packet.data.sliceArray(28 until packet.data.size) // Skip IP + UDP headers

            if (dnsData.size > 12) { // DNS header is 12 bytes
                // Parse DNS response and extract domain -> IP mappings
                // For now, we'll use a simplified approach
                android.util.Log.d("ProxyVpnService", "DNS response intercepted, size: ${dnsData.size}")
            }
        } catch (e: Exception) {
            android.util.Log.e("ProxyVpnService", "Error intercepting DNS response", e)
        }
    }

    /**
     * Record connection success for circuit breaker
     */
    private fun recordConnectionSuccess() {
        if (failedConnectionCount > 0) {
            android.util.Log.i("ProxyVpnService", "SOCKS5 connection successful. Resetting failure count.")
            failedConnectionCount = 0
        }
    }

    /**
     * Record connection failure for circuit breaker
     */
    private fun recordConnectionFailure() {
        failedConnectionCount++
        lastFailureTime = System.currentTimeMillis()
        android.util.Log.w("ProxyVpnService", "SOCKS5 connection failure recorded. Count: $failedConnectionCount/$maxFailures")

        if (failedConnectionCount >= maxFailures) {
            android.util.Log.w("ProxyVpnService", "Circuit breaker opened. Routing traffic directly for ${circuitBreakerTimeout/1000}s")
        }
    }

    /**
     * Route packet directly (bypass proxy)
     */
    private fun routePacketDirectly(
        packet: ByteBuffer,
        vpnOutput: FileOutputStream,
    ) {
        try {
            vpnOutput.write(packet.array(), 0, packet.remaining())
        } catch (e: Exception) {
            // Handle routing errors
        }
    }

    /**
     * Route packet directly (overload for ByteBuffer)
     */
    private fun routePacketDirectly(
        packet: IpPacket,
        vpnOutput: FileOutputStream,
    ) {
        routePacketDirectly(ByteBuffer.wrap(packet.data), vpnOutput)
    }

    /**
     * Create notification channel for Android O+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel =
                NotificationChannel(
                    CHANNEL_ID,
                    "Proxy VPN Service",
                    NotificationManager.IMPORTANCE_LOW,
                ).apply {
                    description = "Proxy VPN service notifications"
                    setShowBadge(false)
                }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Create response packet from proxy data
     */
    private fun createResponsePacket(originalPacket: IpPacket, responseData: ByteArray, dataLength: Int): ByteArray {
        // Simplified response packet creation
        // In a real implementation, this would need proper TCP sequence handling
        val responsePacket = ByteArray(40 + dataLength) // IP + TCP headers + data

        // Copy and modify IP header
        System.arraycopy(originalPacket.data, 0, responsePacket, 0, 20)

        // Swap source and destination IPs
        System.arraycopy(originalPacket.destIp, 0, responsePacket, 12, 4)
        System.arraycopy(originalPacket.sourceIp, 0, responsePacket, 16, 4)

        // Copy and modify TCP header
        System.arraycopy(originalPacket.data, 20, responsePacket, 20, 20)

        // Swap source and destination ports
        val originalTcpHeader = ByteBuffer.wrap(originalPacket.data, 20, 20)
        val sourcePort = originalTcpHeader.short
        val destPort = originalTcpHeader.short

        val responseTcpHeader = ByteBuffer.wrap(responsePacket, 20, 20)
        responseTcpHeader.putShort(0, destPort)
        responseTcpHeader.putShort(2, sourcePort)

        // Copy response data
        System.arraycopy(responseData, 0, responsePacket, 40, dataLength)

        return responsePacket
    }

    /**
     * Create foreground service notification
     */
    private fun createNotification(config: ProxyConfig): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent =
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Proxy VPN Active")
            .setContentText("Routing ${config.domains.size} domains through ${config.serverAddress}")
            .setSmallIcon(R.drawable.ic_vpn_key_24)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
}

/**
 * Data class representing an IP packet
 */
data class IpPacket(
    val version: Int,
    val protocol: Int,
    val sourceIp: ByteArray,
    val destIp: ByteArray,
    val data: ByteArray,
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as IpPacket

        if (version != other.version) return false
        if (protocol != other.protocol) return false
        if (!sourceIp.contentEquals(other.sourceIp)) return false
        if (!destIp.contentEquals(other.destIp)) return false
        if (!data.contentEquals(other.data)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = version
        result = 31 * result + protocol
        result = 31 * result + sourceIp.contentHashCode()
        result = 31 * result + destIp.contentHashCode()
        result = 31 * result + data.contentHashCode()
        return result
    }
}
