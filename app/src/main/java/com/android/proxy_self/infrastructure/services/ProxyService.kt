package com.android.proxy_self.infrastructure.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.android.proxy_self.MainActivity
import com.android.proxy_self.R
import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyStatus
import com.android.proxy_self.domain.entities.ProxyStatusInfo
import com.android.proxy_self.infrastructure.managers.ProxyManager
import com.android.proxy_self.infrastructure.managers.ProxyNotificationManager
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Foreground service for managing proxy operations
 * Maintains proxy connection and provides status updates
 * Implements Single Responsibility Principle
 */
@AndroidEntryPoint
class ProxyService : Service() {
    @Inject
    lateinit var proxyManager: ProxyManager

    @Inject
    lateinit var notificationManager: ProxyNotificationManager

    private val binder = ProxyServiceBinder()
    private var serviceJob: Job? = null
    private var isRunning = false
    private var currentConfig: ProxyConfig? = null

    // State flows for reactive updates
    private val _proxyStatus =
        MutableStateFlow(
            ProxyStatusInfo(status = ProxyStatus.DISCONNECTED),
        )
    val proxyStatus: StateFlow<ProxyStatusInfo> = _proxyStatus.asStateFlow()

    private val _isProxyEnabled = MutableStateFlow(false)
    val isProxyEnabled: StateFlow<Boolean> = _isProxyEnabled.asStateFlow()

    companion object {
        const val ACTION_START_PROXY = "com.android.proxy_self.START_PROXY"
        const val ACTION_STOP_PROXY = "com.android.proxy_self.STOP_PROXY"
        const val ACTION_UPDATE_CONFIG = "com.android.proxy_self.UPDATE_CONFIG"
        const val EXTRA_PROXY_CONFIG = "proxy_config"

        private const val NOTIFICATION_ID = 1000
        private const val CHANNEL_ID = "proxy_service_channel"
    }

    inner class ProxyServiceBinder : Binder() {
        fun getService(): ProxyService = this@ProxyService
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onStartCommand(
        intent: Intent?,
        flags: Int,
        startId: Int,
    ): Int {
        when (intent?.action) {
            ACTION_START_PROXY -> {
                val config =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG, ProxyConfig::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG)
                    }
                config?.let { startProxy(it) }
            }
            ACTION_STOP_PROXY -> {
                stopProxy()
            }
            ACTION_UPDATE_CONFIG -> {
                val config =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG, ProxyConfig::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra(EXTRA_PROXY_CONFIG)
                    }
                config?.let { updateConfig(it) }
            }
        }
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        stopProxy()
    }

    /**
     * Start proxy with given configuration
     */
    fun startProxy(config: ProxyConfig) {
        android.util.Log.d("ProxyService", "startProxy called with config: ${config.serverAddress}:${config.serverPort}")

        if (isRunning) {
            android.util.Log.d("ProxyService", "Proxy already running, stopping first...")
            stopProxy()
            // Small delay to ensure clean stop
            Thread.sleep(500)
        }

        currentConfig = config
        isRunning = true

        android.util.Log.d("ProxyService", "Starting proxy service...")

        // Update status to connecting
        _proxyStatus.value =
            ProxyStatusInfo(
                status = ProxyStatus.CONNECTING,
                activeDomains = config.domains,
            )

        // Start foreground service
        startForeground(NOTIFICATION_ID, createNotification(config))

        // Start proxy operations in background
        serviceJob =
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    android.util.Log.d("ProxyService", "Testing proxy connection...")
                    // Test proxy connection first
                    val isConnected = proxyManager.testProxyConnection(config)

                    if (isConnected) {
                        android.util.Log.d("ProxyService", "Proxy connection successful, starting VPN service...")
                        // Start VPN service for traffic interception
                        startVpnService(config)

                        // Update status to connected
                        _proxyStatus.value =
                            ProxyStatusInfo(
                                status = ProxyStatus.CONNECTED,
                                connectionTime = System.currentTimeMillis(),
                                activeDomains = config.domains,
                            )
                        _isProxyEnabled.value = true

                        // Update notification
                        updateNotification(config, true)
                    } else {
                        // Connection failed
                        _proxyStatus.value =
                            ProxyStatusInfo(
                                status = ProxyStatus.ERROR,
                                lastError = "Failed to connect to proxy server",
                            )
                        stopProxy()
                    }
                } catch (e: Exception) {
                    _proxyStatus.value =
                        ProxyStatusInfo(
                            status = ProxyStatus.ERROR,
                            lastError = e.message ?: "Unknown error",
                        )
                    stopProxy()
                }
            }
    }

    /**
     * Stop proxy service
     */
    fun stopProxy() {
        android.util.Log.d("ProxyService", "stopProxy called, isRunning: $isRunning")

        if (!isRunning) {
            android.util.Log.d("ProxyService", "Proxy not running, ignoring stop request")
            return
        }

        android.util.Log.d("ProxyService", "Stopping proxy service...")
        isRunning = false
        serviceJob?.cancel()

        // Stop VPN service
        android.util.Log.d("ProxyService", "Stopping VPN service...")
        stopVpnService()

        // Update status
        _proxyStatus.value = ProxyStatusInfo(status = ProxyStatus.DISCONNECTED)
        _isProxyEnabled.value = false

        // Stop foreground service
        android.util.Log.d("ProxyService", "Stopping foreground service...")
        stopForeground(true)
        currentConfig = null
    }

    /**
     * Update proxy configuration
     */
    fun updateConfig(config: ProxyConfig) {
        android.util.Log.d("ProxyService", "updateConfig called, isRunning: $isRunning")

        currentConfig = config
        if (isRunning) {
            android.util.Log.d("ProxyService", "Restarting proxy with new configuration...")
            // Stop without terminating service
            stopProxyWithoutTerminating()
            // Small delay
            Thread.sleep(500)
            // Start with new config
            startProxy(config)
        }
    }

    /**
     * Stop proxy without terminating the service
     */
    private fun stopProxyWithoutTerminating() {
        android.util.Log.d("ProxyService", "stopProxyWithoutTerminating called")

        isRunning = false
        serviceJob?.cancel()

        // Stop VPN service
        stopVpnService()

        // Update status
        _proxyStatus.value = ProxyStatusInfo(status = ProxyStatus.DISCONNECTED)
        _isProxyEnabled.value = false

        currentConfig = null
    }

    /**
     * Get current proxy status
     */
    fun getCurrentStatus(): ProxyStatusInfo = _proxyStatus.value

    /**
     * Check if proxy is enabled
     */
    fun isEnabled(): Boolean = _isProxyEnabled.value

    /**
     * Start VPN service for traffic interception
     */
    private fun startVpnService(config: ProxyConfig) {
        val vpnIntent =
            Intent(this, ProxyVpnService::class.java).apply {
                action = ProxyVpnService.ACTION_START_VPN
                putExtra(ProxyVpnService.EXTRA_PROXY_CONFIG, config)
            }
        startService(vpnIntent)
    }

    /**
     * Stop VPN service
     */
    private fun stopVpnService() {
        val vpnIntent =
            Intent(this, ProxyVpnService::class.java).apply {
                action = ProxyVpnService.ACTION_STOP_VPN
            }
        startService(vpnIntent)
    }

    /**
     * Create notification channel for Android O+
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel =
                NotificationChannel(
                    CHANNEL_ID,
                    "Proxy Service",
                    NotificationManager.IMPORTANCE_LOW,
                ).apply {
                    description = "Proxy service notifications"
                    setShowBadge(false)
                }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    /**
     * Create foreground service notification
     */
    private fun createNotification(config: ProxyConfig): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent =
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        val stopIntent =
            Intent(this, ProxyService::class.java).apply {
                action = ACTION_STOP_PROXY
            }
        val stopPendingIntent =
            PendingIntent.getService(
                this,
                1,
                stopIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Proxy Service")
            .setContentText("Connecting to ${config.serverAddress}:${config.serverPort}")
            .setSmallIcon(R.drawable.ic_proxy_24)
            .setContentIntent(pendingIntent)
            .addAction(R.drawable.ic_stop_24, "Stop", stopPendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * Update notification with current status
     */
    private fun updateNotification(
        config: ProxyConfig,
        isConnected: Boolean,
    ) {
        val notification =
            if (isConnected) {
                createConnectedNotification(config)
            } else {
                createNotification(config)
            }

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    /**
     * Create notification for connected state
     */
    private fun createConnectedNotification(config: ProxyConfig): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent =
            PendingIntent.getActivity(
                this,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        val stopIntent =
            Intent(this, ProxyService::class.java).apply {
                action = ACTION_STOP_PROXY
            }
        val stopPendingIntent =
            PendingIntent.getService(
                this,
                1,
                stopIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
            )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Proxy Connected")
            .setContentText("${config.domains.size} domains routed through ${config.serverAddress}")
            .setSmallIcon(R.drawable.ic_proxy_24)
            .setContentIntent(pendingIntent)
            .addAction(R.drawable.ic_stop_24, "Disconnect", stopPendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
}
