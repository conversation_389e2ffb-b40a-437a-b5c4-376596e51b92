package com.android.proxy_self.infrastructure.utils

import java.util.regex.Pattern
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for input validation
 * Implements Single Responsibility Principle
 */
@Singleton
class ValidationUtils
    @Inject
    constructor() {
        companion object {
            // IPv4 regex pattern
            private val IPV4_PATTERN =
                Pattern.compile(
                    "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$",
                )

            // IPv6 regex pattern (simplified)
            private val IPV6_PATTERN =
                Pattern.compile(
                    "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$",
                )

            // Domain name regex pattern
            private val DOMAIN_PATTERN =
                Pattern.compile(
                    "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$",
                )

            // Port range constants
            private const val MIN_PORT = 1
            private const val MAX_PORT = 65535
        }

        /**
         * Validate IP address (IPv4 or IPv6)
         */
        fun isValidIpAddress(ip: String): Boolean {
            if (ip.isBlank()) return false
            return IPV4_PATTERN.matcher(ip).matches() || IPV6_PATTERN.matcher(ip).matches()
        }

        /**
         * Validate IPv4 address specifically
         */
        fun isValidIpv4Address(ip: String): Boolean {
            if (ip.isBlank()) return false
            return IPV4_PATTERN.matcher(ip).matches()
        }

        /**
         * Validate IPv6 address specifically
         */
        fun isValidIpv6Address(ip: String): Boolean {
            if (ip.isBlank()) return false
            return IPV6_PATTERN.matcher(ip).matches()
        }

        /**
         * Validate port number
         */
        fun isValidPort(port: Int): Boolean {
            return port in MIN_PORT..MAX_PORT
        }

        /**
         * Validate port string
         */
        fun isValidPort(port: String): Boolean {
            return try {
                val portInt = port.toInt()
                isValidPort(portInt)
            } catch (e: NumberFormatException) {
                false
            }
        }

        /**
         * Validate domain name
         */
        fun isValidDomain(domain: String): Boolean {
            if (domain.isBlank()) return false

            // Remove leading/trailing whitespace
            val trimmedDomain = domain.trim()

            // Check length
            if (trimmedDomain.length > 253) return false

            // Check pattern
            return DOMAIN_PATTERN.matcher(trimmedDomain).matches()
        }

        /**
         * Validate comma-separated domains and IP addresses
         */
        fun isValidDomainList(domains: String): Boolean {
            if (domains.isBlank()) return false

            val domainList =
                domains.split(",")
                    .map { it.trim() }
                    .filter { it.isNotBlank() }

            if (domainList.isEmpty()) return false

            return domainList.all { isValidDomainOrIp(it) }
        }

        /**
         * Validate domain name or IP address
         */
        fun isValidDomainOrIp(input: String): Boolean {
            if (input.isBlank()) return false
            val trimmed = input.trim()

            // Check if it's a valid IP address first
            if (isValidIpAddress(trimmed)) return true

            // Then check if it's a valid domain
            return isValidDomain(trimmed)
        }

        /**
         * Parse and validate domains/IPs from comma-separated string
         */
        fun parseAndValidateDomains(domains: String): ValidationResult<List<String>> {
            if (domains.isBlank()) {
                return ValidationResult.Error("Domains/IPs cannot be empty")
            }

            val domainList =
                domains.split(",")
                    .map { it.trim() }
                    .filter { it.isNotBlank() }

            if (domainList.isEmpty()) {
                return ValidationResult.Error("No valid domains/IPs found")
            }

            val invalidEntries = domainList.filter { !isValidDomainOrIp(it) }
            if (invalidEntries.isNotEmpty()) {
                return ValidationResult.Error("Invalid domains/IPs: ${invalidEntries.joinToString(", ")}")
            }

            return ValidationResult.Success(domainList)
        }

        /**
         * Validate username
         */
        fun isValidUsername(username: String): Boolean {
            return username.isNotBlank() && username.length <= 255
        }

        /**
         * Validate password
         */
        fun isValidPassword(password: String): Boolean {
            return password.isNotBlank() && password.length <= 255
        }

        /**
         * Get validation error message for IP address
         */
        fun getIpValidationError(ip: String): String? {
            return when {
                ip.isBlank() -> "IP address cannot be empty"
                !isValidIpAddress(ip) -> "Invalid IP address format"
                else -> null
            }
        }

        /**
         * Get validation error message for port
         */
        fun getPortValidationError(port: String): String? {
            return when {
                port.isBlank() -> "Port cannot be empty"
                !isValidPort(port) -> "Port must be between $MIN_PORT and $MAX_PORT"
                else -> null
            }
        }

        /**
         * Get validation error message for username
         */
        fun getUsernameValidationError(username: String): String? {
            return when {
                username.isBlank() -> "Username cannot be empty"
                username.length > 255 -> "Username is too long (max 255 characters)"
                else -> null
            }
        }

        /**
         * Get validation error message for password
         */
        fun getPasswordValidationError(password: String): String? {
            return when {
                password.isBlank() -> "Password cannot be empty"
                password.length > 255 -> "Password is too long (max 255 characters)"
                else -> null
            }
        }

        /**
         * Get validation error message for domains
         */
        fun getDomainsValidationError(domains: String): String? {
            val result = parseAndValidateDomains(domains)
            return if (result is ValidationResult.Error) result.message else null
        }
    }

/**
 * Sealed class for validation results
 */
sealed class ValidationResult<out T> {
    data class Success<T>(val data: T) : ValidationResult<T>()

    data class Error(val message: String) : ValidationResult<Nothing>()
}
