package com.android.proxy_self.infrastructure.managers

import com.android.proxy_self.domain.entities.ProxyConfig
import com.android.proxy_self.domain.entities.ProxyType
import com.android.proxy_self.infrastructure.utils.NetworkUtils
import com.android.proxy_self.infrastructure.utils.Socks5Client
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Authenticator
import okhttp3.Credentials
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager class for proxy operations
 * Handles SOCKS5 and HTTP proxy connections with authentication
 * Implements Single Responsibility Principle
 */
@Singleton
class ProxyManager
    @Inject
    constructor(
        private val networkUtils: NetworkUtils,
    ) {
        /**
         * Test proxy connection with given configuration
         */
        suspend fun testProxyConnection(config: ProxyConfig): Boolean =
            withContext(Dispatchers.IO) {
                try {
                    android.util.Log.d("ProxyManager", "Testing SOCKS5 proxy connection to ${config.serverAddress}:${config.serverPort}")
                    android.util.Log.d("ProxyManager", "Username: ${config.username}, Password: ${if (config.password.isNotEmpty()) "***" else "empty"}")

                    val socks5Client = Socks5Client(config.serverAddress, config.serverPort, config.username, config.password)
                    val result = socks5Client.testConnection(config)

                    android.util.Log.d("ProxyManager", "SOCKS5 test result: $result")
                    result
                } catch (e: Exception) {
                    android.util.Log.e("ProxyManager", "SOCKS5 proxy test failed", e)
                    false
                }
            }

        /**
         * Check if domain should use proxy based on whitelist
         */
        fun shouldUseProxy(
            domain: String,
            whitelist: List<String>,
        ): Boolean {
            // DEBUG MODE: For testing, route all traffic through proxy
            // TODO: Remove this after debugging
            android.util.Log.d("ProxyManager", "shouldUseProxy called with domain: $domain, whitelist: $whitelist")

            // If domain is empty or null, don't use proxy
            if (domain.isBlank()) {
                android.util.Log.d("ProxyManager", "Domain is blank, not using proxy")
                return false
            }

            // Check if domain is in whitelist
            val shouldUse = networkUtils.isDomainInWhitelist(domain, whitelist)
            android.util.Log.d("ProxyManager", "Domain $domain should use proxy: $shouldUse")

            return shouldUse
        }

        /**
         * Create proxy instance based on configuration
         */
        private fun createProxy(config: ProxyConfig): Proxy {
            val proxyType =
                when (config.proxyType) {
                    ProxyType.HTTP -> Proxy.Type.HTTP
                    ProxyType.SOCKS5 -> Proxy.Type.SOCKS
                }

            val socketAddress = InetSocketAddress(config.serverAddress, config.serverPort)
            return Proxy(proxyType, socketAddress)
        }

        /**
         * Create HTTP client with proxy configuration
         */
        private fun createHttpClient(
            config: ProxyConfig,
            proxy: Proxy,
        ): OkHttpClient {
            return OkHttpClient.Builder()
                .proxy(proxy)
                .authenticator(createProxyAuthenticator(config))
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build()
        }

        /**
         * Create proxy authenticator for authentication
         */
        private fun createProxyAuthenticator(config: ProxyConfig): Authenticator {
            return object : Authenticator {
                override fun authenticate(
                    route: Route?,
                    response: Response,
                ): Request? {
                    // Check if this is a proxy authentication challenge
                    if (response.code == 407) {
                        val credential = Credentials.basic(config.username, config.password)
                        return response.request.newBuilder()
                            .header("Proxy-Authorization", credential)
                            .build()
                    }
                    return null
                }
            }
        }

        /**
         * Validate proxy configuration
         */
        fun validateProxyConfig(config: ProxyConfig): ProxyValidationResult {
            val errors = mutableListOf<String>()

            // Validate server address
            if (config.serverAddress.isBlank()) {
                errors.add("Server address cannot be empty")
            }

            // Validate port
            if (config.serverPort !in 1..65535) {
                errors.add("Port must be between 1 and 65535")
            }

            // Validate credentials
            if (config.username.isBlank()) {
                errors.add("Username cannot be empty")
            }

            if (config.password.isBlank()) {
                errors.add("Password cannot be empty")
            }

            // Validate domains
            if (config.domains.isEmpty()) {
                errors.add("At least one domain must be specified")
            }

            return if (errors.isEmpty()) {
                ProxyValidationResult.Valid
            } else {
                ProxyValidationResult.Invalid(errors)
            }
        }

        /**
         * Get proxy connection info for debugging
         */
        fun getProxyConnectionInfo(config: ProxyConfig): ProxyConnectionInfo {
            return ProxyConnectionInfo(
                serverAddress = config.serverAddress,
                serverPort = config.serverPort,
                proxyType = config.proxyType.displayName,
                username = config.username,
                domainCount = config.domains.size,
                domains = config.domains,
            )
        }

        /**
         * Create SOCKS5 client for VPN service
         */
        suspend fun createSocksProxy(config: ProxyConfig): SocksProxyResult {
            return try {
                android.util.Log.d("ProxyManager", "Creating SOCKS5 client for ${config.serverAddress}:${config.serverPort}")

                // Test SOCKS5 connection first
                val socks5Client = Socks5Client(
                    proxyHost = config.serverAddress,
                    proxyPort = config.serverPort,
                    username = config.username,
                    password = config.password
                )

                // Test connection to a known endpoint
                val testSocket = socks5Client.connect("httpbin.org", 80)
                if (testSocket != null) {
                    testSocket.close()
                    android.util.Log.d("ProxyManager", "SOCKS5 proxy connection test successful")
                    SocksProxyResult.Success("SOCKS5 proxy connection established successfully")
                } else {
                    android.util.Log.e("ProxyManager", "SOCKS5 proxy connection test failed")
                    SocksProxyResult.Error("Failed to establish SOCKS5 connection")
                }
            } catch (e: Exception) {
                android.util.Log.e("ProxyManager", "Error creating SOCKS5 proxy", e)
                SocksProxyResult.Error(e.message ?: "Unknown error")
            }
        }

        /**
         * Stop SOCKS proxy connections
         */
        suspend fun stopSocksProxy(): Boolean {
            return try {
                android.util.Log.d("ProxyManager", "Stopping SOCKS5 proxy connections")
                // In a real implementation, this would close all active SOCKS5 connections
                // For now, just return success as connections are managed by VPN service
                true
            } catch (e: Exception) {
                android.util.Log.e("ProxyManager", "Error stopping SOCKS5 proxy", e)
                false
            }
        }
    }

/**
 * Sealed class for proxy validation results
 */
sealed class ProxyValidationResult {
    object Valid : ProxyValidationResult()

    data class Invalid(val errors: List<String>) : ProxyValidationResult()
}

/**
 * Data class for proxy connection information
 */
data class ProxyConnectionInfo(
    val serverAddress: String,
    val serverPort: Int,
    val proxyType: String,
    val username: String,
    val domainCount: Int,
    val domains: List<String>,
)

/**
 * Sealed class for SOCKS proxy results
 */
sealed class SocksProxyResult {
    data class Success(val message: String) : SocksProxyResult()

    data class Error(val message: String) : SocksProxyResult()
}
