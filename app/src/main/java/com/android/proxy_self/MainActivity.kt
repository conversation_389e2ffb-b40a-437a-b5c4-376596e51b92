package com.android.proxy_self

import android.app.Activity
import android.content.Intent
import android.net.VpnService
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.lifecycleScope
import com.android.proxy_self.presentation.ui.navigation.ProxyNavigation
import com.android.proxy_self.presentation.ui.theme.ProxySelfTheme
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * Main activity for the Proxy Self application
 * Uses Jetpack Compose for UI and Hilt for dependency injection
 * Handles VPN permission requests
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    // VPN permission result flow
    private val _vpnPermissionResult = MutableSharedFlow<Boolean>()
    val vpnPermissionResult = _vpnPermissionResult.asSharedFlow()

    // VPN permission launcher
    private val vpnPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        lifecycleScope.launch {
            _vpnPermissionResult.emit(result.resultCode == Activity.RESULT_OK)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            ProxySelfTheme {
                ProxyNavigation(
                    onRequestVpnPermission = ::requestVpnPermission
                )
            }
        }
    }

    /**
     * Request VPN permission from user
     */
    fun requestVpnPermission() {
        val intent = VpnService.prepare(this)
        if (intent != null) {
            vpnPermissionLauncher.launch(intent)
        } else {
            // Permission already granted
            lifecycleScope.launch {
                _vpnPermissionResult.emit(true)
            }
        }
    }

    /**
     * Check if VPN permission is granted
     */
    fun isVpnPermissionGranted(): Boolean {
        val isGranted = VpnService.prepare(this) == null
        android.util.Log.d("MainActivity", "VPN permission granted: $isGranted")
        return isGranted
    }
}
