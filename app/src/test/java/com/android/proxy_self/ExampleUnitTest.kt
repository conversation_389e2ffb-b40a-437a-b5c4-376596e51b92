package com.android.proxy_self

import org.junit.Assert.*
import org.junit.Test

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun `test proxy config creation`() {
        // Given
        val serverAddress = "127.0.0.1"
        val serverPort = 1080

        // When
        val config = mapOf(
            "serverAddress" to serverAddress,
            "serverPort" to serverPort
        )

        // Then
        assertEquals(serverAddress, config["serverAddress"])
        assertEquals(serverPort, config["serverPort"])
    }

    @Test
    fun `test domain list parsing`() {
        // Given
        val domainsString = "google.com,facebook.com,example.com"

        // When
        val domainList = domainsString.split(",").map { it.trim() }

        // Then
        assertEquals(3, domainList.size)
        assertTrue(domainList.contains("google.com"))
        assertTrue(domainList.contains("facebook.com"))
        assertTrue(domainList.contains("example.com"))
    }

    @Test
    fun `test port validation`() {
        // Given
        val validPorts = listOf(80, 443, 1080, 8080)
        val invalidPorts = listOf(-1, 0, 65536, 70000)

        // When & Then
        validPorts.forEach { port ->
            assertTrue("Port $port should be valid", port in 1..65535)
        }

        invalidPorts.forEach { port ->
            assertFalse("Port $port should be invalid", port in 1..65535)
        }
    }
}
