#!/bin/bash

# Test VPN Permission Flow
# This script tests the VPN permission request and proxy functionality

set -e

echo "🔧 Testing VPN Permission Flow..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if device is connected
print_status "Checking device connection..."
if ! adb devices | grep -q "device$"; then
    print_error "No Android device connected. Please connect a device and enable USB debugging."
    exit 1
fi
print_success "Device connected"

# Install the app
print_status "Installing app..."
if ! adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    print_error "Failed to install app"
    exit 1
fi
print_success "App installed successfully"

# Clear app data to start fresh
print_status "Clearing app data..."
adb shell pm clear com.android.proxy_self
print_success "App data cleared"

# Start the app
print_status "Starting app..."
adb shell am start -n com.android.proxy_self/.MainActivity
sleep 3

# Check if app is running
print_status "Checking if app is running..."
if adb shell pidof com.android.proxy_self > /dev/null; then
    print_success "App is running"
else
    print_error "App failed to start"
    exit 1
fi

# Test VPN permission flow
print_status "Testing VPN permission flow..."
echo ""
echo "📱 Manual Test Steps:"
echo "1. Open the app on your device"
echo "2. Fill in proxy configuration:"
echo "   - Server Address: 127.0.0.1"
echo "   - Server Port: 1080"
echo "   - Username: test"
echo "   - Password: test"
echo "   - Domains: example.com"
echo "3. Tap 'Start Proxy' button"
echo "4. You should see VPN permission dialog"
echo "5. Grant VPN permission"
echo "6. Check if proxy starts successfully"
echo ""

# Monitor logs for VPN permission
print_status "Monitoring logs for VPN permission events..."
echo "Press Ctrl+C to stop monitoring"
echo ""

# Start log monitoring in background
adb logcat -c  # Clear logs
adb logcat | grep -E "(ProxyVpnService|VPN|permission|MainActivity)" &
LOGCAT_PID=$!

# Wait for user input
read -p "Press Enter when you've completed the manual test steps..."

# Kill logcat
kill $LOGCAT_PID 2>/dev/null || true

# Check final status
print_status "Checking final app status..."

# Check if VPN service is running
if adb shell ps | grep -q "com.android.proxy_self"; then
    print_success "App processes are running"
else
    print_warning "No app processes found"
fi

# Check for VPN interface
if adb shell ip link show | grep -q "tun"; then
    print_success "VPN interface detected"
else
    print_warning "No VPN interface found"
fi

print_status "Test completed!"
echo ""
echo "🔍 Expected Results:"
echo "✅ VPN permission dialog should appear when starting proxy"
echo "✅ After granting permission, proxy should start successfully"
echo "✅ VPN interface should be created"
echo "✅ App should show 'Connected' status"
echo ""
echo "🐛 If issues found:"
echo "- Check logcat output above for errors"
echo "- Verify VPN permission was granted"
echo "- Check if proxy server is reachable"
