#!/bin/bash

# Comprehensive Android Proxy Debug Script
# Tests proxy functionality step by step with detailed logging

set -e

echo "🔧 Android Proxy Debug & Fix..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Proxy configuration
PROXY_HOST="***************"
PROXY_PORT="3127"
PROXY_USER="n8n_proxy_user"
PROXY_PASS="n8n_proxy_pass"
DOMAIN="whatismyipaddress.com"
PACKAGE_NAME="com.android.proxy_self"

print_status "Starting comprehensive Android proxy debug..."
echo "📱 Proxy Configuration:"
echo "  - Type: SOCKS5"
echo "  - Server: $PROXY_HOST:$PROXY_PORT"
echo "  - Username: $PROXY_USER"
echo "  - Domain: $DOMAIN"
echo ""

# Check device connection
print_status "Step 1: Checking device connection..."
if ! adb devices | grep -q "device$"; then
    print_error "No Android device connected. Please connect device and enable USB debugging."
    exit 1
fi
print_success "Device connected"

# Build and install app
print_status "Step 2: Building and installing app..."
if ! ./gradlew assembleDebug; then
    print_error "Build failed"
    exit 1
fi

if ! adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    print_error "Installation failed"
    exit 1
fi
print_success "App installed"

# Clear app data and logs
print_status "Step 3: Clearing app data and logs..."
adb shell pm clear $PACKAGE_NAME
adb logcat -c
print_success "App data and logs cleared"

# Start app
print_status "Step 4: Starting app..."
adb shell am start -n $PACKAGE_NAME/.MainActivity
sleep 3

if adb shell pidof $PACKAGE_NAME > /dev/null; then
    print_success "App started successfully"
else
    print_error "App failed to start"
    exit 1
fi

# Monitor logs in background
print_status "Step 5: Starting log monitoring..."
adb logcat | grep -E "(ProxyVpnService|ProxyManager|ProxyService|VPN|SOCKS|$PACKAGE_NAME)" > debug_logs.txt &
LOGCAT_PID=$!

# Test proxy connection from Android
print_status "Step 6: Testing proxy connection from Android..."
echo ""
echo "📱 MANUAL STEPS - Please perform on your device:"
echo "1. Open the app"
echo "2. Fill in the proxy configuration:"
echo "   - Server Address: $PROXY_HOST"
echo "   - Server Port: $PROXY_PORT"
echo "   - Username: $PROXY_USER"
echo "   - Password: $PROXY_PASS"
echo "   - Proxy Type: SOCKS5"
echo "   - Domains: $DOMAIN"
echo "3. Tap 'Test Connection' button first"
echo "4. If test passes, tap 'Save' then 'Start Proxy'"
echo "5. Grant VPN permission when prompted"
echo ""

read -p "Press Enter after completing the manual steps above..."

# Stop log monitoring
kill $LOGCAT_PID 2>/dev/null || true

# Analyze logs
print_status "Step 7: Analyzing logs..."
if [ -f debug_logs.txt ]; then
    echo ""
    echo "🔍 Debug Logs Analysis:"
    echo "========================"
    
    # Check for VPN permission
    if grep -q "VPN permission" debug_logs.txt; then
        print_status "VPN permission events found in logs"
        grep "VPN permission" debug_logs.txt | tail -5
    else
        print_warning "No VPN permission events found"
    fi
    
    # Check for proxy connection attempts
    if grep -q "ProxyManager" debug_logs.txt; then
        print_status "Proxy connection attempts found"
        grep "ProxyManager" debug_logs.txt | tail -10
    else
        print_warning "No proxy connection attempts found"
    fi
    
    # Check for SOCKS5 events
    if grep -q "SOCKS" debug_logs.txt; then
        print_status "SOCKS5 events found"
        grep "SOCKS" debug_logs.txt | tail -10
    else
        print_warning "No SOCKS5 events found"
    fi
    
    # Check for errors
    if grep -q -i "error\|exception\|failed" debug_logs.txt; then
        print_error "Errors found in logs:"
        grep -i "error\|exception\|failed" debug_logs.txt | tail -10
    fi
    
    echo ""
    echo "📄 Full debug log saved to: debug_logs.txt"
else
    print_error "No debug logs captured"
fi

# Check VPN interface
print_status "Step 8: Checking VPN interface..."
VPN_INTERFACE=$(adb shell ip link show | grep tun || echo "")
if [ -n "$VPN_INTERFACE" ]; then
    print_success "VPN interface found:"
    echo "$VPN_INTERFACE"
else
    print_warning "No VPN interface found"
fi

# Check app processes
print_status "Step 9: Checking app processes..."
APP_PROCESSES=$(adb shell ps | grep $PACKAGE_NAME || echo "")
if [ -n "$APP_PROCESSES" ]; then
    print_success "App processes running:"
    echo "$APP_PROCESSES"
else
    print_warning "No app processes found"
fi

# Network connectivity test
print_status "Step 10: Testing network connectivity from device..."
print_status "Testing direct connection to proxy server..."
adb shell "timeout 5 nc -z $PROXY_HOST $PROXY_PORT && echo 'Proxy server reachable' || echo 'Proxy server not reachable'"

print_status "Testing DNS resolution..."
adb shell "nslookup $DOMAIN && echo 'DNS resolution works' || echo 'DNS resolution failed'"

echo ""
print_status "Debug completed!"
echo ""
echo "🔍 Common Issues & Solutions:"
echo "1. VPN Permission: Check if VPN permission dialog appeared and was granted"
echo "2. Proxy Server: Verify proxy server is reachable from device network"
echo "3. Domain Matching: Check if domain extraction and matching works"
echo "4. SOCKS5 Implementation: Verify SOCKS5 client implementation"
echo ""
echo "📋 Next Steps:"
echo "- Review debug_logs.txt for detailed error messages"
echo "- Check if proxy server is accessible from device's network"
echo "- Verify VPN service is running and creating tunnel"
