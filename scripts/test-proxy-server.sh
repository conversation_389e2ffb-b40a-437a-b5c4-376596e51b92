#!/bin/bash

# Test SOCKS5 Proxy Server Connectivity
# Tests the provided proxy server before debugging Android app

set -e

echo "🔧 Testing SOCKS5 Proxy Server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Proxy configuration
PROXY_HOST="***************"
PROXY_PORT="3127"
PROXY_USER="n8n_proxy_user"
PROXY_PASS="n8n_proxy_pass"
TEST_URL="http://whatismyipaddress.com"

print_status "Testing proxy server: $PROXY_HOST:$PROXY_PORT"
print_status "Username: $PROXY_USER"
print_status "Test URL: $TEST_URL"
echo ""

# Test 1: Basic connectivity to proxy server
print_status "Test 1: Basic connectivity to proxy server..."
if timeout 10 nc -z "$PROXY_HOST" "$PROXY_PORT" 2>/dev/null; then
    print_success "Proxy server is reachable on $PROXY_HOST:$PROXY_PORT"
else
    print_error "Cannot reach proxy server on $PROXY_HOST:$PROXY_PORT"
    print_error "This could be a network issue or the proxy server is down"
    exit 1
fi

# Test 2: Test with curl using SOCKS5 proxy
print_status "Test 2: Testing HTTP request through SOCKS5 proxy..."
if command -v curl >/dev/null 2>&1; then
    # Test without authentication first
    print_status "Testing without authentication..."
    if timeout 30 curl -s --socks5 "$PROXY_HOST:$PROXY_PORT" "$TEST_URL" >/dev/null 2>&1; then
        print_success "SOCKS5 proxy works without authentication"
    else
        print_warning "SOCKS5 proxy failed without authentication (expected if auth required)"
        
        # Test with authentication
        print_status "Testing with authentication..."
        if timeout 30 curl -s --socks5 "$PROXY_USER:$PROXY_PASS@$PROXY_HOST:$PROXY_PORT" "$TEST_URL" >/dev/null 2>&1; then
            print_success "SOCKS5 proxy works with authentication"
        else
            print_error "SOCKS5 proxy failed with authentication"
            print_error "Check username/password or proxy server configuration"
        fi
    fi
else
    print_warning "curl not available, skipping HTTP test"
fi

# Test 3: Get IP address through proxy
print_status "Test 3: Getting IP address through proxy..."
if command -v curl >/dev/null 2>&1; then
    print_status "Your current IP (direct):"
    timeout 10 curl -s http://ipinfo.io/ip || echo "Failed to get direct IP"
    
    print_status "IP through proxy:"
    timeout 30 curl -s --socks5 "$PROXY_USER:$PROXY_PASS@$PROXY_HOST:$PROXY_PORT" http://ipinfo.io/ip || echo "Failed to get proxy IP"
fi

# Test 4: Test specific domain from whitelist
print_status "Test 4: Testing domain from whitelist..."
WHITELIST_DOMAIN="whatismyipaddress.com"
if timeout 30 curl -s --socks5 "$PROXY_USER:$PROXY_PASS@$PROXY_HOST:$PROXY_PORT" "http://$WHITELIST_DOMAIN" | head -20; then
    print_success "Successfully accessed $WHITELIST_DOMAIN through proxy"
else
    print_error "Failed to access $WHITELIST_DOMAIN through proxy"
fi

echo ""
print_status "Proxy server test completed!"
echo ""
echo "📋 Summary:"
echo "- Proxy Server: $PROXY_HOST:$PROXY_PORT"
echo "- Authentication: $PROXY_USER:$PROXY_PASS"
echo "- Test Domain: $WHITELIST_DOMAIN"
echo ""
echo "🔍 If proxy server tests pass but Android app still fails:"
echo "1. Check VPN permission is granted"
echo "2. Verify domain matching logic"
echo "3. Check packet routing implementation"
echo "4. Review Android VPN service logs"
