#!/bin/bash

# Real-time VPN Debug Monitor
# Monitors VPN service logs in real-time

set -e

echo "🔧 Real-time VPN Debug Monitor..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

PACKAGE_NAME="com.android.proxy_self"

print_status "Starting real-time VPN debug monitor..."
echo "📱 Instructions:"
echo "1. Open the app on your device"
echo "2. Configure proxy settings:"
echo "   - Server: ***************"
echo "   - Port: 3127"
echo "   - Username: n8n_proxy_user"
echo "   - Password: n8n_proxy_pass"
echo "   - Domain: whatismyipaddress.com"
echo "3. Tap 'Test Connection' first"
echo "4. Then tap 'Start Proxy'"
echo "5. Grant VPN permission when prompted"
echo ""
echo "🔍 Monitoring logs in real-time..."
echo "Press Ctrl+C to stop monitoring"
echo ""

# Clear logs and start monitoring
adb logcat -c

# Monitor logs with filters
adb logcat | grep -E "(ProxyVpnService|ProxyManager|ProxyService|VPN|SOCKS|MainActivity.*VPN|$PACKAGE_NAME.*VPN)" --line-buffered | while read line; do
    timestamp=$(echo "$line" | cut -d' ' -f1-2)
    rest=$(echo "$line" | cut -d' ' -f3-)
    
    # Color code different types of logs
    if echo "$line" | grep -q "ProxyVpnService"; then
        echo -e "${GREEN}[$timestamp]${NC} ${BLUE}VPN:${NC} $rest"
    elif echo "$line" | grep -q "VPN permission"; then
        echo -e "${GREEN}[$timestamp]${NC} ${YELLOW}PERMISSION:${NC} $rest"
    elif echo "$line" | grep -q "SOCKS"; then
        echo -e "${GREEN}[$timestamp]${NC} ${BLUE}SOCKS:${NC} $rest"
    elif echo "$line" | grep -q -i "error\|failed\|exception"; then
        echo -e "${GREEN}[$timestamp]${NC} ${RED}ERROR:${NC} $rest"
    elif echo "$line" | grep -q -i "success\|established\|connected"; then
        echo -e "${GREEN}[$timestamp]${NC} ${GREEN}SUCCESS:${NC} $rest"
    else
        echo -e "${GREEN}[$timestamp]${NC} $rest"
    fi
done
